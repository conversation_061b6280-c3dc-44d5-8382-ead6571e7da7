package com.ms.bp.interfaces.rest.controller;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.ms.bp.application.ExportJobStatusService;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.application.data.DataApplicationService;
import com.ms.bp.domain.file.model.ExportJobStatus;
import com.ms.bp.domain.file.model.ImportJobStatus;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.interfaces.dto.response.ExportHistoryResponse;
import com.ms.bp.interfaces.dto.response.ImportHistoryResponse;
import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.common.exception.ValidationException;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.FunctionUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.util.TestDataManager;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * DataController統合テストクラス
 * 実際のDataControllerインスタンスを使用して全体フローを検証する
 * APIGatewayProxyRequestEventは実際のオブジェクトを使用し、ContextとAsyncLambdaInvokerをモック
 * AWS Lambda環境依存を回避するためAsyncLambdaInvokerをモック化
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DataController統合テスト")
class DataControllerIntegrationTest {
    private static final Logger logger = LoggerFactory.getLogger(DataControllerIntegrationTest.class);

    // テスト対象（実際のインスタンス）
    private DataController dataController;

    // 実際のAPIGatewayProxyRequestEventオブジェクト
    private APIGatewayProxyRequestEvent testRequest;

    // モックオブジェクト（Contextのみ）
    @Mock
    private Context mockContext;

    /**
     * AsyncLambdaInvokerのモックオブジェクト
     * テスト環境でAWS Lambda呼び出しを回避するために使用
     */
    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;

    // テストデータ
    private UserInfo testUserInfo;

    // JSON文字列定数
    private static final String VALID_EXPORT_REQUEST_JSON = """
        {
            "dataType": "1",
            "area": ["0000","0200"],
            "hnshBashoKubun": "0",
            "dataKubun": ["0","1"]
        }
        """;

    private static final String VALID_EXPORT_REQUEST_JSON_HEADOFFICE = """
        {
            "dataType": "2",
            "area": ["SKSA","0000"],
            "hnshBashoKubun": "0",
            "dataKubun": ["0"]
        }
        """;

    private static final String VALID_EXPORT_REQUEST_JSON_AREA = """
        {
            "dataType": "3",
            "area": ["1800","0000"],
            "hnshBashoKubun": "0",
            "dataKubun": ["0,1"]
        }
        """;

    private static final String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "1",
            "area": ["0001"]
        }
        """;

    private static final String VALID_IMPORT_REQUEST_JSON_AREA = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "3",
            "area": ["0000","1800"],
            "fileName":"test.csv"
        }
        """;



    @BeforeEach
    void setUp() {
        try {
            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");

            // 実際のDataControllerインスタンスを作成
            // 注意：これによりデータベース接続等の実際の初期化が実行される
            dataController = new DataController();

            // DataApplicationService内のAsyncLambdaInvokerをモックに置き換え
            // リフレクションを使用してプライベートフィールドにアクセス
            Field dataApplicationServiceField = DataController.class.getDeclaredField("dataApplicationService");
            dataApplicationServiceField.setAccessible(true);
            DataApplicationService dataApplicationService = (DataApplicationService) dataApplicationServiceField.get(dataController);

            Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
            asyncLambdaInvokerField.setAccessible(true);
            asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

            // モック動作を設定：非同期呼び出しは何もしない（例外を投げない）
            // lenientを使用してUnnecessaryStubbingExceptionを回避
            lenient().doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any());

            // テスト用UserInfoを作成
            testUserInfo = createTestUserInfo();

            // 実際のAPIGatewayProxyRequestEventオブジェクトを作成
            testRequest = new APIGatewayProxyRequestEvent();

            System.out.println("DataController初期化完了（AsyncLambdaInvokerモック設定済み）");

        } catch (Exception e) {
            System.err.println("DataController初期化エラー: " + e.getMessage());
            e.printStackTrace();
            // 初期化に失敗した場合でもテストを継続するため、nullのままにする
            dataController = null;
        }
    }

    // ==================== エクスポート処理の統合テスト ====================

    @Test
    @DisplayName("エクスポート処理_全体フロー_正常実行確認")
    void testExportData() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }

        // リクエストの設定
        testRequest = createTestRequest(VALID_EXPORT_REQUEST_JSON_HEADOFFICE);

        // コンテキストの設定（lenientを使用してUnnecessaryStubbingExceptionを回避）
        lenient().when(mockContext.getFunctionName()).thenReturn("test-function");
        lenient().when(mockContext.getAwsRequestId()).thenReturn("test-request-id");

        try {
            // When: エクスポート処理を実行
            System.out.println("エクスポート処理開始...");
            CommonResult<?> result = dataController.exportData(testRequest, testUserInfo, mockContext);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            System.out.println("エクスポート処理結果: " + result.getCode());
            System.out.println("エクスポート処理メッセージ: " + result.getMsg());
            
            if (result.getData() != null) {
                System.out.println("エクスポート処理データ: " + result.getData().toString());
            }

            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

            // 非同期Lambda呼び出しが実行されたことを検証（成功時のみ）
            if (result.isSuccess()) {
                verify(mockAsyncLambdaInvoker, times(1)).invokeAsync(any());
                System.out.println("✓ 非同期Lambda呼び出しが正常に実行されました");
            }

        } catch (Exception e) {
            System.err.println("エクスポート処理でエラーが発生: " + e.getMessage());
            e.printStackTrace();

            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("エリアエクスポート処理_全体フロー_正常実行確認")
    void testExportDataArea() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }

        // リクエストの設定
        testRequest = createTestRequest(VALID_EXPORT_REQUEST_JSON_AREA);

        // コンテキストの設定（lenientを使用してUnnecessaryStubbingExceptionを回避）
        lenient().when(mockContext.getFunctionName()).thenReturn("test-function");
        lenient().when(mockContext.getAwsRequestId()).thenReturn("test-request-id");

        try {
            // When: エクスポート処理を実行
            System.out.println("エクスポート処理開始...");
            CommonResult<?> result = dataController.exportData(testRequest, testUserInfo, mockContext);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            System.out.println("エクスポート処理結果: " + result.getCode());
            System.out.println("エクスポート処理メッセージ: " + result.getMsg());

            if (result.getData() != null) {
                System.out.println("エクスポート処理データ: " + result.getData().toString());
            }

            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

            // 非同期Lambda呼び出しが実行されたことを検証（成功時のみ）
            if (result.isSuccess()) {
                verify(mockAsyncLambdaInvoker, times(1)).invokeAsync(any());
                System.out.println("✓ 非同期Lambda呼び出しが正常に実行されました");
            }

        } catch (Exception e) {
            System.err.println("エクスポート処理でエラーが発生: " + e.getMessage());
            e.printStackTrace();

            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    // ==================== インポート処理の統合テスト ====================

    @Test
    @DisplayName("インポート処理_全体フロー_正常実行確認")
    void testImportData() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }

        // リクエストの設定
        testRequest = createTestRequest(VALID_IMPORT_REQUEST_JSON);

        // コンテキストの設定（lenientを使用してUnnecessaryStubbingExceptionを回避）
        lenient().when(mockContext.getFunctionName()).thenReturn("test-function");
        lenient().when(mockContext.getAwsRequestId()).thenReturn("test-request-id");

        try {
            // When: インポート処理を実行
            System.out.println("インポート処理開始...");
            CommonResult<?> result = dataController.importData(testRequest, testUserInfo, mockContext);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            System.out.println("インポート処理結果: " + result.getCode());
            System.out.println("インポート処理メッセージ: " + result.getMsg());
            
            if (result.getData() != null) {
                System.out.println("インポート処理データ: " + result.getData().toString());
            }

            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

            // 非同期Lambda呼び出しが実行されたことを検証（成功時のみ）
            if (result.isSuccess()) {
                verify(mockAsyncLambdaInvoker, times(1)).invokeAsync(any());
                System.out.println("✓ 非同期Lambda呼び出しが正常に実行されました");
            }

        } catch (Exception e) {
            System.err.println("インポート処理でエラーが発生: " + e.getMessage());
            e.printStackTrace();

            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    // ==================== 履歴取得処理の統合テスト ====================

    @Test
    @DisplayName("エクスポート履歴取得_全体フロー_正常実行確認")
    void testGetExportHistory() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }

        // リクエストの設定（履歴取得はボディが不要だが、オブジェクトは必要）
        testRequest = createTestRequest("{}");

        try {
            // When: エクスポート履歴取得を実行
            System.out.println("エクスポート履歴取得開始...");
            CommonResult<?> result = dataController.getExportHistory(testRequest, testUserInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            System.out.println("エクスポート履歴取得結果: " + result.getCode());
            System.out.println("エクスポート履歴取得メッセージ: " + result.getData());
            
            if (result.getData() != null) {
                System.out.println("エクスポート履歴データ件数: " + 
                    (result.getData() instanceof java.util.List ? 
                        ((java.util.List<?>) result.getData()).size() : "不明"));
            }

            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

        } catch (Exception e) {
            System.err.println("エクスポート履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("インポート履歴取得_全体フロー_正常実行確認")
    void testGetImportHistory() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }

        // リクエストの設定（履歴取得はボディが不要だが、オブジェクトは必要）
        testRequest = createTestRequest("{}");

        try {
            // When: インポート履歴取得を実行
            System.out.println("インポート履歴取得開始...");
            CommonResult<?> result = dataController.getImportHistory(testRequest, testUserInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            System.out.println("インポート履歴取得結果: " + result.getCode());
            System.out.println("インポート履歴取得メッセージ: " + result.getData());
            
            if (result.getData() != null) {
                System.out.println("インポート履歴データ件数: " + 
                    (result.getData() instanceof java.util.List ? 
                        ((java.util.List<?>) result.getData()).size() : "不明"));
            }

            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

        } catch (Exception e) {
            System.err.println("インポート履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("エクスポート履歴情報検証_全体フロー_正常実行確認")
    void testGetExportHistoryAreaInfo() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("TESTXX");
        userInfo.setSystemOperationCompanyCode("100001");
        userInfo.setUnitCode("99999");
        userInfo.setAreaCode("0001");
        userInfo.setAreaName("統合テストエリア");
        userInfo.setPositionCode("99");

        try {
            // リクエストの設定
            testRequest = createTestRequest(VALID_EXPORT_REQUEST_JSON_AREA);

            // コンテキストの設定（lenientを使用してUnnecessaryStubbingExceptionを回避）
            lenient().when(mockContext.getFunctionName()).thenReturn("test-function");
            lenient().when(mockContext.getAwsRequestId()).thenReturn("test-request-id");
            // When: エクスポート履歴取得を実行
            System.out.println("エクスポート処理開始...");
            CommonResult<?> exportDataResult = dataController.exportData(testRequest, userInfo, mockContext);


            // リクエストの設定（履歴取得はボディが不要だが、オブジェクトは必要）
            testRequest = createTestRequest("{}");
            System.out.println("エクスポート履歴取得開始...");
            CommonResult<?> result = dataController.getExportHistory(testRequest, userInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            System.out.println("エクスポート履歴取得結果: " + result.getCode());
            System.out.println("エクスポート履歴取得メッセージ: " + result.getMsg());

            if (result.getData() != null) {
                System.out.println("エクスポート履歴データ件数: " +
                        (result.getData() instanceof java.util.List ?
                                ((java.util.List<?>) result.getData()).size() : "不明"));

                List<ExportHistoryResponse> resultList = (List<ExportHistoryResponse>)result.getData();

                for (ExportHistoryResponse res:resultList){

                    // 履歴番号チェック
                    assertNotNull(res.getHistoryNumber(), "レスポンスの履歴番号が設定されていること");

                    // 履歴のファイル種別チェック
                    assertNotNull(res.getFileType(), "レスポンスのファイル種別が設定されていること");

                    // 履歴のエリア情報チェック
                    assertNotNull(res.getArea(), "レスポンスのエリア情報が設定されていること");

                    // 履歴のデータ区分情報チェック
                    List<String> dataDivision = List.of("移管前" ,"移管後", "移管前,移管後");
                    assertNotNull(res.getDataDivision(), "レスポンスのデータ区分情報が設定されていること");
                    // 履歴のデータ区分情報内容チェック
                    assertTrue(dataDivision.contains(res.getDataDivision()), "レスポンスのデータ区分情報内容が正しく設定されていること");

                    // 履歴のファイル作成開始日時チェック
                    assertNotNull(res.getFileCreationStartDateTime(), "レスポンスのファイル作成開始日時が設定されていること");

                    // 履歴のステータスチェック
                    List<String> dataStatus = List.of("処理中" ,"完了", "一部失敗","失敗");
                    assertNotNull(res.getStatus(), "レスポンスのステータスが設定されていること");
                    // 履歴のステータス情報内容チェック
                    assertTrue(dataStatus.contains(res.getStatus()), "レスポンスのステータス情報内容が正しく設定されていること");

                }
            }

            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

        } catch (Exception e) {
            System.err.println("エクスポート履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("インポート履歴取得_全体フロー_正常実行確認")
    void testGetImportHistoryAreaInfo() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("TESTXX");
        userInfo.setSystemOperationCompanyCode("100001");
        userInfo.setUnitCode("99999");
        userInfo.setAreaCode("0001");
        userInfo.setAreaName("統合テストエリア");
        userInfo.setPositionCode("99");

        try {
            // リクエストの設定
            testRequest = createTestRequest(VALID_IMPORT_REQUEST_JSON_AREA);

            // コンテキストの設定（lenientを使用してUnnecessaryStubbingExceptionを回避）
            lenient().when(mockContext.getFunctionName()).thenReturn("test-function");
            lenient().when(mockContext.getAwsRequestId()).thenReturn("test-request-id");
            // When: インポートを実行
            System.out.println("インポート処理開始...");
            CommonResult<?> importDataResult = dataController.importData(testRequest, userInfo, mockContext);


            // リクエストの設定（履歴取得はボディが不要だが、オブジェクトは必要）
            testRequest = createTestRequest("{}");
            // When: インポート履歴取得を実行
            System.out.println("インポート履歴取得開始...");
            CommonResult<?> result = dataController.getImportHistory(testRequest, userInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            System.out.println("インポート履歴取得結果: " + result.getCode());
            System.out.println("インポート履歴取得メッセージ: " + result.getMsg());

            if (result.getData() != null) {
                System.out.println("インポート履歴データ件数: " +
                        (result.getData() instanceof java.util.List ?
                                ((java.util.List<?>) result.getData()).size() : "不明"));
                List<ImportHistoryResponse> resultList = (List<ImportHistoryResponse>)result.getData();

                for (ImportHistoryResponse res:resultList){

                    // 履歴番号チェック
                    assertNotNull(res.getHistoryNumber(), "レスポンスの履歴番号が設定されていること");

                    // 履歴のファイル種別チェック
                    assertNotNull(res.getFileType(), "レスポンスのファイル種別が設定されていること");

                    // 履歴のエリア情報チェック
                    assertNotNull(res.getArea(), "レスポンスのエリア情報が設定されていること");

                    // 履歴のファイル作成開始日時チェック
                    assertNotNull(res.getUploadStartDateTime(), "レスポンスのファイル作成開始日時が設定されていること");

                    // 履歴のステータスチェック
                    List<String> dataStatus = List.of("処理中" ,"完了", "一部失敗","失敗");
                    assertNotNull(res.getStatus(), "レスポンスのステータスが設定されていること");
                    // 履歴のステータス情報内容チェック
                    assertTrue(dataStatus.contains(res.getStatus()), "レスポンスのステータス情報内容が正しく設定されていること");

                }
            }

            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

        } catch (Exception e) {
            System.err.println("インポート履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();

            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    // ==================== 履歴取得処理異常の統合テスト ====================

    @Test
    @DisplayName("エクスポート履歴情報検証_全体フロー_異常実行確認")
    void testGetExportHistoryAreaInfoException() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }
        // リクエストの設定（履歴取得はボディが不要だが、オブジェクトは必要）
        testRequest = createTestRequest("{}");

        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("123");
        userInfo.setSystemOperationCompanyCode("100002");
        userInfo.setUnitCode("99999");
        userInfo.setAreaCode("0001");
        userInfo.setAreaName("統合テストエリア");
        userInfo.setPositionCode("99");

        try {
            // When: エクスポート履歴取得を実行
            System.out.println("エクスポート履歴取得開始...");
            CommonResult<?> result = dataController.getExportHistory(testRequest, userInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

            // エクスポート履歴データを確認
            assertNotNull(result.getData(), "レスポンスコードが設定されていること");

            System.out.println("エクスポート履歴取得結果: " + result.getCode());
            System.out.println("エクスポート履歴取得メッセージ: " + result.getMsg());

            if (result.getData() != null) {
                System.out.println("エクスポート履歴データ件数: " +
                        (result.getData() instanceof java.util.List ?
                                ((java.util.List<?>) result.getData()).size() : "不明"));

                assertEquals(0, ((java.util.List<?>) result.getData()).size(), "エクスポート履歴データ件数が０件のこと");
            }

        } catch (Exception e) {
            System.err.println("エクスポート履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("インポート履歴情報検証_全体フロー_異常実行確認")
    void testGetImportHistoryAreaInfoException() {
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }
        // リクエストの設定（履歴取得はボディが不要だが、オブジェクトは必要）
        testRequest = createTestRequest("{}");

        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("123");
        userInfo.setSystemOperationCompanyCode("100002");
        userInfo.setUnitCode("99999");
        userInfo.setAreaCode("0001");
        userInfo.setAreaName("統合テストエリア");
        userInfo.setPositionCode("99");

        try {
            // When: インポート履歴取得を実行
            System.out.println("インポート履歴取得開始...");
            CommonResult<?> result = dataController.getImportHistory(testRequest, userInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");
            // 成功またはビジネスエラーのいずれかであることを確認
            assertNotNull(result.getCode(), "レスポンスコードが設定されていること");

            // インポート履歴データを確認
            assertNotNull(result.getData(), "レスポンスコードが設定されていること");

            System.out.println("インポート履歴取得結果: " + result.getCode());
            System.out.println("インポート履歴取得メッセージ: " + result.getMsg());

            if (result.getData() != null) {
                System.out.println("インポート履歴データ件数: " +
                        (result.getData() instanceof java.util.List ?
                                ((java.util.List<?>) result.getData()).size() : "不明"));

                assertEquals(0, ((java.util.List<?>) result.getData()).size(), "インポート履歴データ件数が０件のこと");
            }

        } catch (Exception e) {
            System.err.println("インポート履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }


    // ==================== エクスポート リクエストパラメータチェックの統合テスト ====================

    /**
     * リクエストパラメータチェック_次年度計画マスタ_エクスポート_正常系
     * リクエストパターン：
     *         データタイプ：1（次年度計画マスタ）
     *         エリアリスト：["1800"]（データあり）
     *レスポンス検証点：
     *         レスポンスコード：0
     *         レスポンスデータ：データあり
     *         レスポンスメッセージ：なし
     */
    @Test
    @DisplayName("リクエストパラメータチェック_次年度計画マスタ_エクスポート_正常系1")
    void test1_checkParameter_jinendo_mst() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_JINENDO = """
        {
            "dataType":"1",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[]
        }
        """;

        // リクエストパラメータチェックを実施
        checkParameter("export",testUserInfo,VALID_EXPORT_REQUEST_JSON_JINENDO);
    }

    /**
     * リクエストパラメータチェック_次年度計画マスタ_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ：（データなし）
     *         エリアリスト：["1800"]（データなし）
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：データなし
     *         レスポンスメッセージ：パラメータ：ファイル種別、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_次年度計画マスタ_エクスポート_異常系2")
    void test2_checkParameter_jinendo_mst_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_JINENDO = """
        {
            "dataType":"",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル種別、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_JINENDO,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_次年度計画マスタ_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ：1（次年度計画マスタ）
     *         エリアリスト：[""]（データなし）
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：データなし
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_次年度計画マスタ_エクスポート_異常系3")
    void test3_checkParameter_jinendo_mst_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_JINENDO = """
        {
            "dataType":"1",
            "area":[""],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_JINENDO,expectCode,expectMsg);
    }


    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_正常系
     * リクエストパターン：
     *         データタイプ：2
     *         エリアリスト：["1800"]（データあり）
     *         データ区分：["0","1"]
     *レスポンス検証点：
     *         レスポンスコード：0
     *         レスポンスデータ：データあり
     *         レスポンスメッセージ：なし
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_正常系1")
    void test1_checkParameter_hnsh_mtsh() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"2",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":["0","1"]
        }
        """;

        // リクエストパラメータチェックを実施
        checkParameter("export",testUserInfo,VALID_EXPORT_REQUEST_JSON_HNSH_MTSH);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ：""
     *         エリアリスト：["1800"]（データあり）
     *         データ区分：["0","1"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル種別、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系2")
    void test2_checkParameter_hnsh_mtsh_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":["0","1"]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル種別、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_HNSH_MTSH,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："2"
     *         エリアリスト：[""]
     *         データ区分：["0","1"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系3")
    void test3_checkParameter_hnsh_mtsh_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"2",
            "area":[""],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":["0","1"]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_HNSH_MTSH,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："2"
     *         エリアリスト：["1800"]
     *         データ区分：[""]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：データ区分、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系4")
    void test4_checkParameter_hnsh_mtsh_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"2",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[""]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：データ区分、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_HNSH_MTSH,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："2"
     *         エリアリスト：[""]
     *         データ区分：[""]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー\nパラメータ：データ区分、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系5")
    void test5_checkParameter_hnsh_mtsh_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"2",
            "area":[],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[""]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー\nパラメータ：データ区分、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_HNSH_MTSH,expectCode,expectMsg);

    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_正常系
     * リクエストパターン：
     *         データタイプ：3
     *         エリアリスト：["1800"]（データあり）
     *         データ区分：["0","1"]
     *レスポンス検証点：
     *         レスポンスコード：0
     *         レスポンスデータ：データあり
     *         レスポンスメッセージ：なし
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_正常系1")
    void test1_checkParameter_area_mtsh() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"3",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":["0","1"]
        }
        """;

        // リクエストパラメータチェックを実施
        checkParameter("export",testUserInfo,VALID_EXPORT_REQUEST_JSON_HNSH_MTSH);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ：""
     *         エリアリスト：["1800"]（データあり）
     *         データ区分：["0","1"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル種別、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系2")
    void test2_checkParameter_area_mtsh_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":["0","1"]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル種別、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_HNSH_MTSH,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："3"
     *         エリアリスト：[""]
     *         データ区分：["0","1"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系3")
    void test3_checkParameter_area_mtsh_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"3",
            "area":[""],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":["0","1"]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_HNSH_MTSH,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："3"
     *         エリアリスト：["1800"]
     *         データ区分：[""]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：データ区分、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系4")
    void test4_checkParameter_area_mtsh_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"3",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[""]
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：データ区分、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_HNSH_MTSH,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："3"
     *         エリアリスト：[""]
     *         データ区分：[""]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー\nパラメータ：データ区分、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系5")
    void test5_checkParameter_area_mtsh_exception() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON_HNSH_MTSH = """
        {
            "dataType":"3",
            "area":[],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[""]
        }
        """;
        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー\nパラメータ：データ区分、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON_HNSH_MTSH,expectCode,expectMsg);
    }


    /**
     * リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_正常系
     * リクエストパターン：
     *         データタイプ：4
     *         エリアリスト：["1800"]（データあり）
     *         本社・場所区分："2"
     *         カテゴリー："5"
     *レスポンス検証点：
     *         レスポンスコード：0
     *         レスポンスデータ：データあり
     *         レスポンスメッセージ：なし
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_正常系1")
    void test1_checkParameter_indirectProfit_maker() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON = """
        {
            "dataType":"4",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[""]
        }
        """;
        // リクエストパラメータチェックを実施
        checkParameter("export",testUserInfo,VALID_EXPORT_REQUEST_JSON);
    }

    /**
     * リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ：
     *         エリアリスト：["1800"]（データあり）
     *         本社・場所区分："2"
     *         カテゴリー："5"
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：ファイル種別、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系2")
    void test2_checkParameter_indirectProfit_maker() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON = """
        {
            "dataType":"",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[""]
        }
        """;
        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル種別、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："4"
     *         エリアリスト：[""]
     *         本社・場所区分："2"
     *         カテゴリー："5"
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系3")
    void test3_checkParameter_indirectProfit_maker() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON = """
        {
            "dataType":"4",
            "area":[""],
            "hnshBashoKubun":"2",
            "ctgryKubun":"5",
            "dataKubun":[""]
        }
        """;
        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："4"
     *         エリアリスト：["1800"]
     *         本社・場所区分：""
     *         カテゴリー："5"
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：本社・場所区分、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系4")
    void test4_checkParameter_indirectProfit_maker() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON = """
        {
            "dataType":"4",
            "area":["1800"],
            "hnshBashoKubun":"",
            "ctgryKubun":"5",
            "dataKubun":[""]
        }
        """;
        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：本社・場所区分、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："4"
     *         エリアリスト：["1800"]
     *         本社・場所区分："2"
     *         カテゴリー：""
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：カテゴリー、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系5")
    void test5_checkParameter_indirectProfit_maker() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON = """
        {
            "dataType":"4",
            "area":["1800"],
            "hnshBashoKubun":"2",
            "ctgryKubun":"",
            "dataKubun":[""]
        }
        """;
        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：カテゴリー、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON,expectCode,expectMsg);
    }


    /**
     * リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系
     * リクエストパターン：
     *         データタイプ："4"
     *         エリアリスト：[""]
     *         本社・場所区分：""
     *         カテゴリー：""
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー\nパラメータ：カテゴリー、エラー内容：必須チェックエラー\nパラメータ：本社・場所区分、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_異常系6")
    void test6_checkParameter_indirectProfit_maker() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_EXPORT_REQUEST_JSON = """
        {
            "dataType":"4",
            "area":[""],
            "hnshBashoKubun":"",
            "ctgryKubun":"",
            "dataKubun":[""]
        }
        """;
        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー\nパラメータ：カテゴリー、エラー内容：必須チェックエラー\nパラメータ：本社・場所区分、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("export",testUserInfo, VALID_EXPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    // ==================== インポート リクエストパラメータチェックの統合テスト ====================

    /**
     * リクエストパラメータチェック_次年度計画マスタ_インポート_正常系
     * リクエストパターン：
     *         ファイル種別：1
     *         ファイル名：test/sample-file.csv
     *レスポンス検証点：
     *         レスポンスコード：0
     *         レスポンスデータ：データあり
     *         レスポンスメッセージ：なし
     */
    @Test
    @DisplayName("リクエストパラメータチェック_次年度計画マスタ_インポート_正常系1")
    void test1_checkParameter_jinendo_mst_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "1",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // リクエストパラメータチェックを実施
        checkParameter("import",testUserInfo,VALID_IMPORT_REQUEST_JSON);
    }

    /**
     * リクエストパラメータチェック_次年度計画マスタ_インポート_異常系
     * リクエストパターン：
     *         ファイル種別：
     *         ファイル名：test/sample-file.csv
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：ファイル種別、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_次年度計画マスタ_インポート_異常系2")
    void test2_checkParameter_jinendo_mst_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル種別、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_次年度計画マスタ_インポート_異常系
     * リクエストパターン：
     *         ファイル種別：1
     *         S3ファイル名：
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_次年度計画マスタ_インポート_異常系3")
    void test3_checkParameter_jinendo_mst_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "",
            "dataType": "1",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }
    /**
     * リクエストパラメータチェック_次年度計画マスタ_インポート_異常系
     * リクエストパターン：
     *         ファイル種別：1
     *         ファイル名：
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_次年度計画マスタ_インポート_異常系4")
    void test4_checkParameter_jinendo_mst_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test.csv",
            "dataType": "1",
            "area": [""],
            "fileName":""
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_インポート_正常系
     * リクエストパターン：
     *         ファイル種別：2
     *         ファイル名：test/sample-file.csv
     *         エリアリスト：["1800"]
     *レスポンス検証点：
     *         レスポンスコード：0
     *         レスポンスデータ：データあり
     *         レスポンスメッセージ：なし
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_インポート_正常系1")
    void test1_checkParameter_hnsh_mtsh_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "2",
            "area": ["1800"],
            "fileName":"test.csv"
        }
        """;

        // リクエストパラメータチェックを実施
        checkParameter("import",testUserInfo,VALID_IMPORT_REQUEST_JSON);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：
     *         ファイル名：test/sample-file.csv
     *         エリアリスト：["1800"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル種別、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系2")
    void test2_checkParameter_hnsh_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "",
            "area": ["1800"],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル種別、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：2
     *         ファイル名：test/sample-file.csv
     *         エリアリスト：[""]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系3")
    void test3_checkParameter_hnsh_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "2",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：2
     *         S3ファイル名：
     *         エリアリスト：["1800"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系4")
    void test4_checkParameter_hnsh_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "",
            "dataType": "2",
            "area": ["1800"],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：2
     *         ファイル名：
     *         エリアリスト：[""]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー\nパラメータ：エリア、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系5")
    void test5_checkParameter_hnsh_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "",
            "dataType": "2",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー\nパラメータ：エリア、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：2
     *         ファイル名：
     *         エリアリスト：["1800"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_本社_エクスポート_異常系6")
    void test6_checkParameter_hnsh_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test.csv",
            "dataType": "2",
            "area": ["1800"],
            "fileName":""
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_インポート_正常系
     * リクエストパターン：
     *         ファイル種別：3
     *         ファイル名：test/sample-file.csv
     *         エリアリスト：["1800"]
     *レスポンス検証点：
     *         レスポンスコード：0
     *         レスポンスデータ：データあり
     *         レスポンスメッセージ：なし
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_インポート_正常系1")
    void test1_checkParameter_area_mtsh_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "3",
            "area": ["1800"],
            "fileName":"test.csv"
        }
        """;

        // リクエストパラメータチェックを実施
        checkParameter("import",testUserInfo,VALID_IMPORT_REQUEST_JSON);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：
     *         ファイル名：test/sample-file.csv
     *         エリアリスト：["1800"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル種別、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系2")
    void test2_checkParameter_area_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "",
            "area": ["1800"],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル種別、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：2
     *         ファイル名：test/sample-file.csv
     *         エリアリスト：[""]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：エリア、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系3")
    void test3_checkParameter_area_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "3",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：エリア、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：2
     *         S3ファイル名：
     *         エリアリスト：["1800"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系4")
    void test4_checkParameter_area_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "",
            "dataType": "3",
            "area": ["1800"],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：2
     *         ファイル名：
     *         エリアリスト：[""]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー\nパラメータ：エリア、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系5")
    void test5_checkParameter_area_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "",
            "dataType": "3",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー\nパラメータ：エリア、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系
     * リクエストパターン：
     *         ファイル種別：2
     *         ファイル名：
     *         エリアリスト：["1800"]
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：なし
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_見通し・計画_エリア_エクスポート_異常系6")
    void test6_checkParameter_area_mtsh_exception_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample_test.csv",
            "dataType": "3",
            "area": ["1800"],
            "fileName":""
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_間接利益計画＿メーカー別_インポート_正常系
     * リクエストパターン：
     *         ファイル種別：4
     *         ファイル名：test/sample-file.csv
     *レスポンス検証点：
     *         レスポンスコード：0
     *         レスポンスデータ：データあり
     *         レスポンスメッセージ：なし
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画＿メーカー別_インポート_正常系1")
    void test1_checkParameter_indirectProfit_maker_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "4",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // リクエストパラメータチェックを実施
        checkParameter("import",testUserInfo,VALID_IMPORT_REQUEST_JSON);
    }

    /**
     * リクエストパラメータチェック_間接利益計画＿メーカー別_インポート_異常系
     * リクエストパターン：
     *         ファイル種別：
     *         ファイル名：test/sample-file.csv
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：ファイル種別、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画＿メーカー別_インポート_異常系2")
    void test2_checkParameter_indirectProfit_maker_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample-file.csv",
            "dataType": "",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル種別、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_間接利益計画＿メーカー別_インポート_異常系
     * リクエストパターン：
     *         ファイル種別：4
     *         S3ファイル名：
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画＿メーカー別_インポート_異常系3")
    void test3_checkParameter_indirectProfit_maker_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "",
            "dataType": "4",
            "area": [""],
            "fileName":"test.csv"
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    /**
     * リクエストパラメータチェック_間接利益計画＿メーカー別_インポート_異常系
     * リクエストパターン：
     *         ファイル種別：4
     *         ファイル名：
     *レスポンス検証点：
     *         レスポンスコード：1019
     *         レスポンスデータ：
     *         レスポンスメッセージ：パラメータ：ファイル名、エラー内容：必須チェックエラー
     */
    @Test
    @DisplayName("リクエストパラメータチェック_間接利益計画＿メーカー別_インポート_異常系4")
    void test4_checkParameter_indirectProfit_maker_import() {
        // given:テストデータを設定
        // テスト用UserInfoを作成
        testUserInfo = createTestUserInfo();

        //　テスト用リクエスト情報
        String VALID_IMPORT_REQUEST_JSON = """
        {
            "s3Key": "test/sample_test.csv",
            "dataType": "4",
            "area": [""],
            "fileName":""
        }
        """;

        // レスポンス情報
        Integer expectCode = 1019;
        String expectMsg = "パラメータ：ファイル名、エラー内容：必須チェックエラー";
        // リクエストパラメータチェックを実施
        checkParameterException("import",testUserInfo, VALID_IMPORT_REQUEST_JSON,expectCode,expectMsg);
    }

    // ==================== 履歴取得処理の統合テスト ====================

    /**
     * リクエストパラメータチェック_次年度計画マスタ_エクスポート_正常系
     * リクエストパターン：
     *         データタイプ：1（次年度計画マスタ）
     */

    @Test
    @DisplayName("エクスポート履歴情報検証_履歴取得処理_次年度計画マスタ")
    void test1_GetExportHistory_planmaster() {
        try {
            // テストユーザー情報の設定
            UserInfo userInfo = createTestUserInfoXY();

            // エクスポートリクエスト情報の設定
            ExportRequest exportRequest = new ExportRequest();
            exportRequest.setDataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);
            exportRequest.setArea(List.of("1800"));
            exportRequest.setDataKubun(null);

            // When: エクスポート履歴取得を実行
            System.out.println("エクスポート処理開始...");
            // basic exportJobを取得
            ExportJobStatus basicJob = getBasicExportJob(userInfo, exportRequest);

            // テストを実施

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分なし
                ・データ区分なし
                ・カテゴリーなし
             */
            basicJob.setArea("");
            basicJob.setHnshBashoKubun(null);
            basicJob.setDataKubun(null);
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj(null);
            basicJob.setStts("");
            basicJob.setCtgryKubun(null);
            basicJob.setZipFileSize(null);
            basicJob.setZipFileMei(null);
            // 履歴番号を取得
            Long rrkBango1 = insertDownloadRrk(basicJob);

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("");
            basicJob.setHnshBashoKubun("0");
            basicJob.setDataKubun("0");
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj("20250818120800");
            basicJob.setStts("1");
            basicJob.setCtgryKubun("2");
            basicJob.setZipFileSize("20 KB");
            basicJob.setZipFileMei("out/jinendoKkkMst/2025/08/18/事業計画_202508181208.zip");
            // 履歴番号を取得
            Long rrkBango2 = insertDownloadRrk(basicJob);


            /*
            履歴情報パターン：
                ・エリア情報あり
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("1800");
            basicJob.setHnshBashoKubun("0");
            basicJob.setDataKubun("0");
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj("20250818120800");
            basicJob.setStts("1");
            basicJob.setCtgryKubun("2");
            basicJob.setZipFileSize("20 KB");
            basicJob.setZipFileMei("out/jinendoKkkMst/2025/08/18/事業計画_202508181208.zip");
            // 履歴番号を取得
            Long rrkBango3 = insertDownloadRrk(basicJob);

            // チェックを実施
            List<Long> rrkBangos = List.of(rrkBango3,rrkBango2,rrkBango1);
            checkHistory("export", userInfo, null, getExportJobStatusFromDatabase(rrkBangos));

            System.out.println("エクスポート処理終了...");
        } catch (Exception e) {
            System.err.println("履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * リクエストパラメータチェック_見通し・計画_＜本社＞_エクスポート_正常系
     * リクエストパターン：
     *         データタイプ：2（見通し・計画_＜本社＞）
     */

    @Test
    @DisplayName("エクスポート履歴情報検証_履歴取得処理_見通し・計画_＜本社＞")
    void test2_GetExportHistory_headOffice() {
        try {
            // テストユーザー情報の設定
            UserInfo userInfo = createTestUserInfoXY();

            // エクスポートリクエスト情報の設定
            ExportRequest exportRequest = new ExportRequest();
            exportRequest.setDataType(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE);
            exportRequest.setArea(List.of("1800"));
            exportRequest.setDataKubun(null);

            // When: エクスポート履歴取得を実行
            System.out.println("エクスポート処理開始...");
            // basic exportJobを取得
            ExportJobStatus basicJob = getBasicExportJob(userInfo, exportRequest);

            // テストを実施

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分なし
                ・データ区分なし
                ・カテゴリーなし
             */
            basicJob.setArea("");
            basicJob.setHnshBashoKubun(null);
            basicJob.setDataKubun(null);
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj(null);
            basicJob.setStts("");
            basicJob.setCtgryKubun(null);
            basicJob.setZipFileSize(null);
            basicJob.setZipFileMei(null);
            // 履歴番号を取得
            Long rrkBango1 = insertDownloadRrk(basicJob);

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("");
            basicJob.setHnshBashoKubun("0");
            basicJob.setDataKubun("0");
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj("20250818120800");
            basicJob.setStts("1");
            basicJob.setCtgryKubun("2");
            basicJob.setZipFileSize("20 KB");
            basicJob.setZipFileMei("out/mtshKkkHnsh/2025/08/18/事業計画_202508181208.zip");
            // 履歴番号を取得
            Long rrkBango2 = insertDownloadRrk(basicJob);


            /*
            履歴情報パターン：
                ・エリア情報あり
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("1800");
            basicJob.setHnshBashoKubun("0");
            basicJob.setDataKubun("0");
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj("20250818120800");
            basicJob.setStts("1");
            basicJob.setCtgryKubun("2");
            basicJob.setZipFileSize("20 KB");
            basicJob.setZipFileMei("out/mtshKkkHnsh/2025/08/18/事業計画_202508181208.zip");
            // 履歴番号を取得
            Long rrkBango3 = insertDownloadRrk(basicJob);

            // チェックを実施
            List<Long> rrkBangos = List.of(rrkBango3,rrkBango2,rrkBango1);
            checkHistory("export", userInfo, null, getExportJobStatusFromDatabase(rrkBangos));

            System.out.println("エクスポート処理終了...");
        } catch (Exception e) {
            System.err.println("履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * リクエストパラメータチェック_見通し・計画_＜エリア＞_エクスポート_正常系
     * リクエストパターン：
     *         データタイプ：3（見通し・計画_＜エリア＞）
     */

    @Test
    @DisplayName("エクスポート履歴情報検証_履歴取得処理_見通し・計画_＜エリア＞")
    void test3_GetExportHistory_area() {
        try {
            // テストユーザー情報の設定
            UserInfo userInfo = createTestUserInfoXY();

            // エクスポートリクエスト情報の設定
            ExportRequest exportRequest = new ExportRequest();
            exportRequest.setDataType(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE);
            exportRequest.setArea(List.of("1800"));
            exportRequest.setDataKubun(null);

            // When: エクスポート履歴取得を実行
            System.out.println("エクスポート処理開始...");
            // basic exportJobを取得
            ExportJobStatus basicJob = getBasicExportJob(userInfo, exportRequest);

            // テストを実施

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分なし
                ・データ区分なし
                ・カテゴリーなし
             */
            basicJob.setArea("");
            basicJob.setHnshBashoKubun(null);
            basicJob.setDataKubun(null);
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj(null);
            basicJob.setStts("");
            basicJob.setCtgryKubun(null);
            basicJob.setZipFileSize(null);
            basicJob.setZipFileMei(null);
            // 履歴番号を取得
            Long rrkBango1 = insertDownloadRrk(basicJob);

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("");
            basicJob.setHnshBashoKubun("0");
            basicJob.setDataKubun("0");
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj("20250818120800");
            basicJob.setStts("1");
            basicJob.setCtgryKubun("2");
            basicJob.setZipFileSize("20 KB");
            basicJob.setZipFileMei("out/mtshKkkArea/2025/08/18/事業計画_202508181208.zip");
            // 履歴番号を取得
            Long rrkBango2 = insertDownloadRrk(basicJob);


            /*
            履歴情報パターン：
                ・エリア情報あり
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("1800");
            basicJob.setHnshBashoKubun("0");
            basicJob.setDataKubun("0");
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj("20250818120800");
            basicJob.setStts("1");
            basicJob.setCtgryKubun("2");
            basicJob.setZipFileSize("20 KB");
            basicJob.setZipFileMei("out/mtshKkkArea/2025/08/18/事業計画_202508181208.zip");
            // 履歴番号を取得
            Long rrkBango3 = insertDownloadRrk(basicJob);

            // チェックを実施
            List<Long> rrkBangos = List.of(rrkBango3,rrkBango2,rrkBango1);
            checkHistory("export", userInfo, null, getExportJobStatusFromDatabase(rrkBangos));

            System.out.println("エクスポート処理終了...");
        } catch (Exception e) {
            System.err.println("履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * リクエストパラメータチェック_間接利益計画_メーカー別_エクスポート_正常系
     * リクエストパターン：
     *         データタイプ：4（間接利益計画_メーカー別）
     */

    @Test
    @DisplayName("エクスポート履歴情報検証_履歴取得処理_間接利益計画_メーカー別")
    void test4_GetExportHistory_indirectProfit() {
        try {
            // テストユーザー情報の設定
            UserInfo userInfo = createTestUserInfoXY();

            // エクスポートリクエスト情報の設定
            ExportRequest exportRequest = new ExportRequest();
            exportRequest.setDataType(BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE);
            exportRequest.setArea(List.of("1800"));
            exportRequest.setDataKubun(null);

            // When: エクスポート履歴取得を実行
            System.out.println("エクスポート処理開始...");
            // basic exportJobを取得
            ExportJobStatus basicJob = getBasicExportJob(userInfo, exportRequest);

            // テストを実施

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分なし
                ・データ区分なし
                ・カテゴリーなし
             */
            basicJob.setArea("");
            basicJob.setHnshBashoKubun(null);
            basicJob.setDataKubun(null);
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj(null);
            basicJob.setStts("");
            basicJob.setCtgryKubun(null);
            basicJob.setZipFileSize(null);
            basicJob.setZipFileMei(null);
            // 履歴番号を取得
            Long rrkBango1 = insertDownloadRrk(basicJob);

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("");
            basicJob.setHnshBashoKubun("0");
            basicJob.setDataKubun("0");
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj("20250818120800");
            basicJob.setStts("1");
            basicJob.setCtgryKubun("2");
            basicJob.setZipFileSize("20 KB");
            basicJob.setZipFileMei("out/knstsRiekiMaker/2025/08/18/事業計画_202508181208.zip");
            // 履歴番号を取得
            Long rrkBango2 = insertDownloadRrk(basicJob);


            /*
            履歴情報パターン：
                ・エリア情報あり
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("1800");
            basicJob.setHnshBashoKubun("0");
            basicJob.setDataKubun("0");
            basicJob.setFileSksKshNchj("20250818120000");
            basicJob.setFileSksKnrNchj("20250818120800");
            basicJob.setStts("1");
            basicJob.setCtgryKubun("2");
            basicJob.setZipFileSize("20 KB");
            basicJob.setZipFileMei("out/knstsRiekiMaker/2025/08/18/事業計画_202508181208.zip");
            // 履歴番号を取得
            Long rrkBango3 = insertDownloadRrk(basicJob);

            // チェックを実施
            List<Long> rrkBangos = List.of(rrkBango3,rrkBango2,rrkBango1);
            checkHistory("export", userInfo, null, getExportJobStatusFromDatabase(rrkBangos));

            System.out.println("エクスポート処理終了...");
        } catch (Exception e) {
            System.err.println("履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * リクエストパラメータチェック_次年度計画マスタ_インポート_正常系
     * リクエストパターン：
     *         データタイプ：1（次年度計画マスタ）
     */

    @Test
    @DisplayName("インポート履歴情報検証_履歴取得処理_次年度計画マスタ")
    void test1_GetImportHistory_planmaster() {
        try {
            // テストユーザー情報の設定
            UserInfo userInfo = createTestUserInfoXY();

            // エクスポートリクエスト情報の設定
            ImportRequest importRequest = new ImportRequest();
            importRequest.setDataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);
            importRequest.setArea(List.of("1800"));
            importRequest.setFileName("file.csv");
            importRequest.setS3Key("test/sample-file.csv");

            // When: エクスポート履歴取得を実行
            System.out.println("インポート処理開始...");
            // basic exportJobを取得
            ImportJobStatus basicJob = getBasicImportJob(userInfo, importRequest);

            // テストを実施

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分なし
                ・データ区分なし
                ・カテゴリーなし
             */
            basicJob.setArea(null);
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj(null);
            basicJob.setFileMei(null);
            basicJob.setStts("1");
            basicJob.setErrorFileMei(null);
            // 履歴番号を取得
            Long rrkBango1 = insertUploadRrk(basicJob);

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea(null);
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj("20250818120600");
            basicJob.setFileMei("import_data.csv");
            basicJob.setStts("1");
            basicJob.setErrorFileMei("import-errors/error_20250818120000.txt");
            // 履歴番号を取得
            Long rrkBango2 = insertUploadRrk(basicJob);


            /*
            履歴情報パターン：
                ・エリア情報あり
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("1600,1500");
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj("20250818120600");
            basicJob.setFileMei("import_data.csv");
            basicJob.setStts("1");
            basicJob.setErrorFileMei("import-errors/error_20250818120000.txt");
            // 履歴番号を取得
            Long rrkBango3 = insertUploadRrk(basicJob);

            // チェックを実施
            List<Long> rrkBangos = List.of(rrkBango3,rrkBango2,rrkBango1);
            checkHistory("import", userInfo, getImportJobStatusFromDatabase(rrkBangos), null);

            System.out.println("インポート処理終了...");
        } catch (Exception e) {
            System.err.println("履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * リクエストパラメータチェック_見通し・計画_＜本社＞_インポート_正常系
     * リクエストパターン：
     *         データタイプ：2（見通し・計画_＜本社＞）
     */

    @Test
    @DisplayName("インポート履歴情報検証_履歴取得処理_見通し・計画_＜本社＞")
    void test2_GetImportHistory_headOffice() {
        try {
            // テストユーザー情報の設定
            UserInfo userInfo = createTestUserInfoXY();

            // エクスポートリクエスト情報の設定
            ImportRequest importRequest = new ImportRequest();
            importRequest.setDataType(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE);
            importRequest.setArea(List.of("1800"));
            importRequest.setFileName("file.csv");
            importRequest.setS3Key("test/sample-file.csv");

            // When: エクスポート履歴取得を実行
            System.out.println("インポート処理開始...");
            // basic exportJobを取得
            ImportJobStatus basicJob = getBasicImportJob(userInfo, importRequest);

            // テストを実施

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分なし
                ・データ区分なし
                ・カテゴリーなし
             */
            basicJob.setArea(null);
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj(null);
            basicJob.setFileMei(null);
            basicJob.setStts("1");
            basicJob.setErrorFileMei(null);
            // 履歴番号を取得
            Long rrkBango1 = insertUploadRrk(basicJob);

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea(null);
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj("20250818120600");
            basicJob.setFileMei("import_data.csv");
            basicJob.setStts("1");
            basicJob.setErrorFileMei("import-errors/error_20250818120000.txt");
            // 履歴番号を取得
            Long rrkBango2 = insertUploadRrk(basicJob);


            /*
            履歴情報パターン：
                ・エリア情報あり
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("1600,1500");
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj("20250818120600");
            basicJob.setFileMei("import_data.csv");
            basicJob.setStts("1");
            basicJob.setErrorFileMei("import-errors/error_20250818120000.txt");
            // 履歴番号を取得
            Long rrkBango3 = insertUploadRrk(basicJob);

            // チェックを実施
            List<Long> rrkBangos = List.of(rrkBango3,rrkBango2,rrkBango1);
            checkHistory("import", userInfo, getImportJobStatusFromDatabase(rrkBangos), null);

            System.out.println("インポート処理終了...");
        } catch (Exception e) {
            System.err.println("履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * リクエストパラメータチェック_見通し・計画_＜エリア＞_インポート_正常系
     * リクエストパターン：
     *         データタイプ：3（見通し・計画_＜エリア＞）
     */

    @Test
    @DisplayName("インポート履歴情報検証_履歴取得処理_見通し・計画_＜エリア＞")
    void test3_GetImportHistory_area() {
        try {
            // テストユーザー情報の設定
            UserInfo userInfo = createTestUserInfoXY();

            // エクスポートリクエスト情報の設定
            ImportRequest importRequest = new ImportRequest();
            importRequest.setDataType(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE);
            importRequest.setArea(List.of("1800"));
            importRequest.setFileName("file.csv");
            importRequest.setS3Key("test/sample-file.csv");

            // When: エクスポート履歴取得を実行
            System.out.println("インポート処理開始...");
            // basic exportJobを取得
            ImportJobStatus basicJob = getBasicImportJob(userInfo, importRequest);

            // テストを実施

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分なし
                ・データ区分なし
                ・カテゴリーなし
             */
            basicJob.setArea(null);
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj(null);
            basicJob.setFileMei(null);
            basicJob.setStts("1");
            basicJob.setErrorFileMei(null);
            // 履歴番号を取得
            Long rrkBango1 = insertUploadRrk(basicJob);

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea(null);
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj("20250818120600");
            basicJob.setFileMei("import_data.csv");
            basicJob.setStts("1");
            basicJob.setErrorFileMei("import-errors/error_20250818120000.txt");
            // 履歴番号を取得
            Long rrkBango2 = insertUploadRrk(basicJob);


            /*
            履歴情報パターン：
                ・エリア情報あり
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("1600,1500");
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj("20250818120600");
            basicJob.setFileMei("import_data.csv");
            basicJob.setStts("1");
            basicJob.setErrorFileMei("import-errors/error_20250818120000.txt");
            // 履歴番号を取得
            Long rrkBango3 = insertUploadRrk(basicJob);

            // チェックを実施
            List<Long> rrkBangos = List.of(rrkBango3,rrkBango2,rrkBango1);
            checkHistory("import", userInfo, getImportJobStatusFromDatabase(rrkBangos), null);

            System.out.println("インポート処理終了...");
        } catch (Exception e) {
            System.err.println("履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * リクエストパラメータチェック_間接利益計画_メーカー別_インポート_正常系
     * リクエストパターン：
     *         データタイプ：4（間接利益計画_メーカー別）
     */

    @Test
    @DisplayName("インポート履歴情報検証_履歴取得処理_間接利益計画_メーカー別")
    void test4_GetImportHistory_indirectProfit() {
        try {
            // テストユーザー情報の設定
            UserInfo userInfo = createTestUserInfoXY();

            // エクスポートリクエスト情報の設定
            ImportRequest importRequest = new ImportRequest();
            importRequest.setDataType(BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE);
            importRequest.setArea(List.of("1800"));
            importRequest.setFileName("file.csv");
            importRequest.setS3Key("test/sample-file.csv");

            // When: エクスポート履歴取得を実行
            System.out.println("インポート処理開始...");
            // basic exportJobを取得
            ImportJobStatus basicJob = getBasicImportJob(userInfo, importRequest);

            // テストを実施

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分なし
                ・データ区分なし
                ・カテゴリーなし
             */
            basicJob.setArea(null);
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj(null);
            basicJob.setFileMei(null);
            basicJob.setStts("1");
            basicJob.setErrorFileMei(null);
            // 履歴番号を取得
            Long rrkBango1 = insertUploadRrk(basicJob);

            /*
            履歴情報パターン：
                ・エリア情報なし
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea(null);
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj("20250818120600");
            basicJob.setFileMei("import_data.csv");
            basicJob.setStts("1");
            basicJob.setErrorFileMei("import-errors/error_20250818120000.txt");
            // 履歴番号を取得
            Long rrkBango2 = insertUploadRrk(basicJob);


            /*
            履歴情報パターン：
                ・エリア情報あり
                ・本社、エリア区分あり
                ・データ区分あり
                ・カテゴリーあり
             */
            basicJob.setArea("1600,1500");
            basicJob.setUploadKshNchj("20250818120000");
            basicJob.setUploadKnrNchj("20250818120600");
            basicJob.setFileMei("import_data.csv");
            basicJob.setStts("1");
            basicJob.setErrorFileMei("import-errors/error_20250818120000.txt");
            // 履歴番号を取得
            Long rrkBango3 = insertUploadRrk(basicJob);

            // チェックを実施
            List<Long> rrkBangos = List.of(rrkBango3,rrkBango2,rrkBango1);
            checkHistory("import", userInfo, getImportJobStatusFromDatabase(rrkBangos), null);

            System.out.println("インポート処理終了...");
        } catch (Exception e) {
            System.err.println("履歴取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    // ==================== プライベートヘルパーメソッド ====================

    /**
     * データベースからExportJobStatusを取得
     * 状態表データの検証用
     *
     * @param rrkBangos 履歴番号リスト
     */
    private List<ExportJobStatus> getExportJobStatusFromDatabase(List<Long> rrkBangos) {
        try {
            List<ExportJobStatus> exportJobStatuses = new ArrayList<>();
            // 真実のExportJobStatusServiceインスタンスを使用してデータベースから取得
            rrkBangos.forEach(rrkBango ->{
                ExportJobStatusService exportJobStatusService = new ExportJobStatusService();
                exportJobStatuses.add(exportJobStatusService.getJobStatus(rrkBango));
            });
            return exportJobStatuses;
        } catch (Exception e) {
            logger.error("ExportJobStatus取得エラー: rrkBango={}, error={}", rrkBangos, e.getMessage(), e);
            return null;
        }
    }

    /**
     * データベースからImportJobStatusを取得
     * 状態表データの検証用
     *
     * @param rrkBangos 履歴番号リスト
     */
    private List<ImportJobStatus> getImportJobStatusFromDatabase(List<Long> rrkBangos) {
        try {
            List<ImportJobStatus> importJobStatuses = new ArrayList<>();
            // 真実のExportJobStatusServiceインスタンスを使用してデータベースから取得
            rrkBangos.forEach(rrkBango -> {
                ImportJobStatusService importJobStatusService = new ImportJobStatusService();
                importJobStatuses.add(importJobStatusService.getJobStatus(rrkBango));
            });
            return importJobStatuses;
        } catch (Exception e) {
            logger.error("ImportJobStatus取得エラー: rrkBango={}, error={}", rrkBangos, e.getMessage(), e);
            return null;
        }
    }


    private ExportJobStatus getBasicExportJob(UserInfo userInfo, ExportRequest exportRequest){
        String functionId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_DOWNLOAD_CODE, exportRequest.getDataType()).getFunctionId();
        // ジョブステータスを初期化（データベースに保存）
        return ExportJobStatus.builder()
                .systmUnyoKigyoCode(userInfo.getSystemOperationCompanyCode())
                .shainCode(userInfo.getShainCode())
                .fileShbts(exportRequest.getDataType())
                .area(exportRequest.getAreaString())
                .hnshBashoKubun(exportRequest.getHnshBashoKubun())
                .dataKubun(exportRequest.getDataKubunString())
                .ctgryKubun(exportRequest.getCtgryKubun())
                .build().init(functionId);
    }

    private ImportJobStatus getBasicImportJob(UserInfo userInfo, ImportRequest importRequest){
        String functionId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE, importRequest.getDataType()).getFunctionId();
        // ジョブステータスを初期化（データベースに保存）
        return ImportJobStatus.builder()
                .systmUnyoKigyoCode(userInfo.getSystemOperationCompanyCode())
                .shainCode(userInfo.getShainCode())
                .fileShbts(importRequest.getDataType())
                .area(importRequest.getAreaString())
                .fileMei(importRequest.getFileName())
                .build().init(functionId);
    }

    /**
     * インポートジョブステータスを挿入し、自動生成された履歴番号を返す
     * RRK_BANGOフィールドは自動生成されるため、INSERT文から除外
     * @param jobStatus インポートジョブステータス
     * @return 自動生成された履歴番号
     * @throws SQLException SQL実行エラー
     */
    public Long insertUploadRrk(ImportJobStatus jobStatus) throws SQLException {

        String sql = """
            INSERT INTO t_upload_rrk (
                SYSTM_UNYO_KIGYO_CODE, SHAIN_CODE, FILE_SHBTS, AREA,
                FILE_MEI, ERROR_FILE_MEI, UPLOAD_KSH_NCHJ, UPLOAD_KNR_NCHJ, STTS,
                TRK_PRGRM_ID, TRK_SYSTM_UNYO_KIGYO_CODE, TRK_SHAIN_CODE,
                KSHN_PRGRM_ID, KSHN_SYSTM_UNYO_KIGYO_CODE, KSHN_SHAIN_CODE,
                VRSN, RCRD_TRK_NCHJ, RCRD_KSHN_NCHJ
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        Object[] params = {
                jobStatus.getSystmUnyoKigyoCode(),
                jobStatus.getShainCode(),
                jobStatus.getFileShbts(),
                jobStatus.getArea(),
                jobStatus.getFileMei(),
                jobStatus.getErrorFileMei(),
                jobStatus.getUploadKshNchj(),
                jobStatus.getUploadKnrNchj(),
                jobStatus.getStts(),
                jobStatus.getTrkPrgrmId(),
                jobStatus.getTrkSystmUnyoKigyoCode(),
                jobStatus.getTrkShainCode(),
                jobStatus.getKshnPrgrmId(),
                jobStatus.getKshnSystmUnyoKigyoCode(),
                jobStatus.getKshnShainCode(),
                jobStatus.getVrsn(),
                jobStatus.getRcrdTrkNchj(),
                jobStatus.getRcrdKshnNchj()
        };

        return LambdaResourceManager.executeWithTransaction(serviceFactory -> {
            JdbcTemplate jdbcTemplate = serviceFactory.getJdbcTemplate();


            // 自動生成された履歴番号を取得
            Long generatedRrkBango = null;
            try {
                generatedRrkBango = jdbcTemplate.insertWithGeneratedKey(sql, params, "rrk_bango");
                logger.debug("インポートジョブステータスを挿入しました: rrkBango={}", generatedRrkBango);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            return generatedRrkBango;

        });
    }

    /**
     * エクスポートジョブステータスを挿入し、自動生成された履歴番号を返す
     * @param jobStatus エクスポートジョブステータス
     * @return 自動生成された履歴番号
     * @throws SQLException SQL実行エラー
     */
    private Long insertDownloadRrk(ExportJobStatus jobStatus) throws SQLException {
        String sql = """
                INSERT INTO T_DWNLD_RRK (
                     SYSTM_UNYO_KIGYO_CODE, SHAIN_CODE, FILE_SHBTS, AREA,
                    HNSH_BASHO_KUBUN, DATA_KUBUN, FILE_SKS_KSH_NCHJ, FILE_SKS_KNR_NCHJ,
                    STTS, ZIP_FILE_SIZE, ZIP_FILE_MEI, TRK_PRGRM_ID, TRK_SYSTM_UNYO_KIGYO_CODE,
                    TRK_SHAIN_CODE, KSHN_PRGRM_ID, KSHN_SYSTM_UNYO_KIGYO_CODE, KSHN_SHAIN_CODE,
                    VRSN, RCRD_TRK_NCHJ, RCRD_KSHN_NCHJ,CTGRY_KUBUN
                ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

        Object[] params = {
                jobStatus.getSystmUnyoKigyoCode(),
                jobStatus.getShainCode(),
                jobStatus.getFileShbts(),
                jobStatus.getArea(),
                jobStatus.getHnshBashoKubun(),
                jobStatus.getDataKubun(),
                jobStatus.getFileSksKshNchj(),
                jobStatus.getFileSksKnrNchj(),
                jobStatus.getStts(),
                jobStatus.getZipFileSize(),
                jobStatus.getZipFileMei(),
                jobStatus.getTrkPrgrmId(),
                jobStatus.getTrkSystmUnyoKigyoCode(),
                jobStatus.getTrkShainCode(),
                jobStatus.getKshnPrgrmId(),
                jobStatus.getKshnSystmUnyoKigyoCode(),
                jobStatus.getKshnShainCode(),
                jobStatus.getVrsn(),
                jobStatus.getRcrdTrkNchj(),
                jobStatus.getRcrdKshnNchj(),
                jobStatus.getCtgryKubun()
        };
        return LambdaResourceManager.executeWithTransaction(serviceFactory -> {
            JdbcTemplate jdbcTemplate = serviceFactory.getJdbcTemplate();

            // 自動生成された履歴番号を取得
            Long generatedRrkBango = null;
            try {
                // 自動生成された履歴番号を取得
                generatedRrkBango = jdbcTemplate.insertWithGeneratedKey(sql, params, "rrk_bango");
                logger.debug("エクスポートジョブステータスを挿入しました: rrkBango={}", generatedRrkBango);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            return generatedRrkBango;
        });
    }

    /**
     * 履歴情報チェック共通　正常系
     * @param exportOrImport　エクスポートまたはインポートの区分
     * @param userInfo　ユーザー情報
     * @param originImportData　インポート情報
     * @param originExportData　エクスポート情報
     */
    private void checkHistory(String exportOrImport, UserInfo userInfo, List<ImportJobStatus> originImportData, List<ExportJobStatus> originExportData){
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }
        try {
            // リクエストの設定
            testRequest = createTestRequest("{}");

            // コンテキストの設定（lenientを使用してUnnecessaryStubbingExceptionを回避）
            lenient().when(mockContext.getFunctionName()).thenReturn("test-function");
            lenient().when(mockContext.getAwsRequestId()).thenReturn("test-request-id");
            // When: インポートを実行
            CommonResult<?> DataResult = null;
            System.out.println("履歴情報チェック処理開始...");
            if("export".equals(exportOrImport)){
                DataResult = dataController.getExportHistory(testRequest, userInfo);
            }else {
                DataResult = dataController.getImportHistory(testRequest, userInfo);
            }

            // Then: 結果を検証
            assertNotNull(DataResult, "レスポンスがnullではないこと");
            assertNotNull(DataResult.getCode(), "レスポンスのコードがnullではないこと");
            assertNotNull(DataResult.getData(), "レスポンスのデータがnullではないこと");
            assertNotNull(DataResult.getMsg(), "レスポンスのメッセージがnullではないこと");


            System.out.println("履歴データ件数: " +
                    (DataResult.getData() instanceof List ?
                            ((List<?>) DataResult.getData()).size() : "不明"));

            final Map<String, String> FILE_TYPE_MAPPING = new HashMap<String, String>() {{
                put("1", "次年度計画マスタ");
                put("2", "見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜本社＞");
                put("3", "見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜エリア＞");
                put("4", "間接利益計画_メーカー別");
            }};

            final Map<String, String> IMPORT_FILE_TYPE_MAPPING = new HashMap<String, String>() {{
                put("1", "次年度計画マスタ");
                put("2", "見通し・計画_採算管理単位C別＜本社＞");
                put("3", "見通し・計画_採算管理単位C別＜エリア＞");
                put("4", "間接利益計画_メーカー別");
            }};

            final Map<String, String> HNSHBASHO_MAPPING = new HashMap<String, String>() {{
                put("0", "本社");
                put("1", "場所");
                put("2", "本社・場所");
            }};

            final Map<String, String> DATA_KUBUN_MAPPING = new HashMap<String, String>() {{
                put("0", "移管前");
                put("1", "移管後");
                put("0,1", "移管前,移管後");
            }};

            final Map<String, String> STATUS_MAPPING = new HashMap<String, String>() {{
                put("0", "処理中");
                put("1", "完了");
                put("2", "一部失敗");
                put("3", "失敗");
                put("4", "失敗");
            }};

            final Map<String, String> CTGRY_MAPPING = new HashMap<String, String>() {{
                put("1", "加食");
                put("2", "低温");
                put("3", "酒類");
                put("4", "菓子");
                put("5", "全カテゴリー");
            }};


            if("export".equals(exportOrImport)){
                List<ExportHistoryResponse> resultList = (List<ExportHistoryResponse>)DataResult.getData();
                assertEquals(resultList.size(), originExportData.size());
                IntStream.range(0, resultList.size())
                        .forEach(i -> {
                            ExportHistoryResponse res = resultList.get(i);
                            ExportJobStatus origin = originExportData.get(i);
                            assertEquals(res.getHistoryNumber(), origin.getRrkBango());
                            // ファイルタイプチェック
                            assertEquals(res.getFileType(), !StringUtils.isBlank(origin.getFileShbts()) ? FILE_TYPE_MAPPING.get(origin.getFileShbts().trim()) : "");
                            // エリア情報チェック
                            if (!StringUtils.isBlank(origin.getArea())){
                                assertNotNull(res.getArea());
                            }
                            // 本社・エリア区分
                            assertEquals(res.getHeadquartersLocationDivision(), !StringUtils.isBlank(origin.getHnshBashoKubun()) ? HNSHBASHO_MAPPING.get(origin.getHnshBashoKubun().trim()) : "");
                            // データ区分
                            assertEquals(res.getDataDivision(), !StringUtils.isBlank(origin.getDataKubun()) ? DATA_KUBUN_MAPPING.get(origin.getDataKubun().trim()) : "");

                            assertEquals(res.getFileCreationStartDateTime(), DateUtil.parseDateTime(origin.getFileSksKshNchj()));
                            assertEquals(res.getFileCreationCompletionDateTime(), DateUtil.parseDateTime(origin.getFileSksKnrNchj()));
                            // ステータスチェック
                            assertEquals(res.getStatus(), !StringUtils.isBlank(origin.getStts()) ? STATUS_MAPPING.get(origin.getStts().trim()) : "");
                            // カテゴリーチェック
                            assertEquals(res.getCtgryKubun(), !StringUtils.isBlank(origin.getCtgryKubun()) ? CTGRY_MAPPING.get(origin.getCtgryKubun().trim()) : "");
                            assertEquals(res.getZipSize(), !StringUtils.isBlank(origin.getZipFileSize()) ? origin.getZipFileSize() : "");
                            assertEquals(res.getZipFileName(), !StringUtils.isBlank(origin.getZipFileMei()) ? origin.getZipFileMei() : "");
                        });

            }else {
                List<ImportHistoryResponse> resultList = (List<ImportHistoryResponse>)DataResult.getData();
                assertEquals(resultList.size(), originImportData.size());
                IntStream.range(0, resultList.size())
                        .forEach(i -> {
                            ImportHistoryResponse res = resultList.get(i);
                            ImportJobStatus origin = originImportData.get(i);
                            assertEquals(res.getHistoryNumber(), origin.getRrkBango());
                            // ファイルタイプチェック
                            assertEquals(res.getFileType(), !StringUtils.isBlank(origin.getFileShbts()) ? IMPORT_FILE_TYPE_MAPPING.get(origin.getFileShbts().trim()) : "");
                            // エリア情報チェック
                            if (!StringUtils.isBlank(origin.getArea())){
                                assertNotNull(res.getArea());
                            }
                            assertEquals(res.getUploadStartDateTime(), DateUtil.parseDateTime(origin.getUploadKshNchj()));
                            assertEquals(res.getUploadCompleteDateTime(), DateUtil.parseDateTime(origin.getUploadKnrNchj()));
                            assertEquals(res.getFileName(), origin.getFileMei());
                            assertEquals(res.getErrorFileName(), origin.getErrorFileMei());
                            // ステータスチェック
                            assertEquals(res.getStatus(), !StringUtils.isBlank(origin.getStts()) ? STATUS_MAPPING.get(origin.getStts().trim()) : "");
                        });

            }
        } catch (Exception e) {
            System.err.println("履歴情報チェックでエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
        finally {
            if ("export".equals(exportOrImport)){
                IntStream.range(0, originExportData.size())
                        .forEach(i -> {
                            ExportJobStatus origin = originExportData.get(i);
                            TestDataManager.deleteTableData("T_DWNLD_RRK", List.of(Map.of("RRK_BANGO", origin.getRrkBango())));
                        });
                System.out.println("データを削除しました: テーブル=T_DWNLD_RRK, 削除件数=" + originExportData.size() + ", 履歴番号=" + originExportData.stream().map(origin -> String.valueOf(origin.getRrkBango())).collect(Collectors.joining(",")));
            }else {
                IntStream.range(0, originImportData.size())
                        .forEach(i -> {
                            ImportJobStatus origin = originImportData.get(i);
                            TestDataManager.deleteTableData("T_UPLOAD_RRK", List.of(Map.of("RRK_BANGO", origin.getRrkBango())));
                        });
                System.out.println("データを削除しました: テーブル=T_UPLOAD_RRK, 削除件数=" + originImportData.size() + ", 履歴番号=" + originImportData.stream().map(origin -> String.valueOf(origin.getRrkBango())).collect(Collectors.joining(",")));
            }
        }
    }

    /**
     * リクエストパラメータチェック共通　正常系
     * @param exportOrImport　エクスポートまたはインポートの区分
     * @param userInfo　ユーザー情報
     * @param requestParameter　リクエスト情報
     */
    private void checkParameter(String exportOrImport, UserInfo userInfo, String requestParameter){
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }
        try {
            // リクエストの設定
            testRequest = createTestRequest(requestParameter);
            // コンテキストの設定（lenientを使用してUnnecessaryStubbingExceptionを回避）
            lenient().when(mockContext.getFunctionName()).thenReturn("test-function");
            lenient().when(mockContext.getAwsRequestId()).thenReturn("test-request-id");
            // When: インポートを実行
            CommonResult<?> DataResult = null;
            System.out.println("リクエストパラメータチェック処理開始...");
            if("export".equals(exportOrImport)){
                DataResult = dataController.exportData(testRequest, userInfo, mockContext);
            }else {
                DataResult = dataController.importData(testRequest, userInfo, mockContext);
            }

            // レスポンスコード確認
            assertNotNull(DataResult.getCode(), "レスポンスコードが設定されていること");
            assertEquals(0, DataResult.getCode(), "レスポンスコードが正常のこと");

            // レスポンスデータ確認
            assertNotNull(DataResult.getData(), "レスポンスデータが設定されていること");

            // レスポンスメッセージ確認
            assertNotNull(DataResult.getMsg(), "レスポンスメッセージが設定されていること");
            assertEquals("", DataResult.getMsg(), "レスポンスメッセージが正常のメッセージこと");

            System.out.printf("code: %S\ndata: %S\nmsg: %S%n", DataResult.getCode(),DataResult.getData(),DataResult.getMsg());
            System.out.println("リクエストパラメータチェック処理終了...");
        } catch (Exception e) {
            System.err.println("リクエストパラメータチェックでエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * リクエストパラメータチェック共通　異常系
     * @param exportOrImport エクスポートまたはインポートの区分
     * @param userInfo　ユーザー情報
     * @param requestParameter　リクエスト情報
     * @param expectCode　レスポンスの期待コード
     * @param expectMsg　レスポンスの期待メッセージ
     */
    private void checkParameterException(String exportOrImport, UserInfo userInfo, String requestParameter, Integer expectCode, String expectMsg){
        // Given: DataControllerが正常に初期化されていることを確認
        if (dataController == null) {
            System.out.println("DataController初期化失敗のため、テストをスキップします");
            return;
        }
        try {
            // リクエストの設定
            testRequest = createTestRequest(requestParameter);
            // コンテキストの設定（lenientを使用してUnnecessaryStubbingExceptionを回避）
            lenient().when(mockContext.getFunctionName()).thenReturn("test-function");
            lenient().when(mockContext.getAwsRequestId()).thenReturn("test-request-id");
            // When: インポートを実行
            CommonResult<?> DataResult = null;
            System.out.println("リクエストパラメータチェック処理開始...");
            if("export".equals(exportOrImport)){
                DataResult = dataController.exportData(testRequest, userInfo, mockContext);
            }else {
                DataResult = dataController.importData(testRequest, userInfo, mockContext);
            }

        } catch (ValidationException e) {
            // バリデーションエラーの場合
            // レスポンスコード確認
            assertNotNull(e.getCode(), "レスポンスコードが設定されていること");
            assertEquals(expectCode, e.getCode(), "レスポンスコードが不正");
            // レスポンスメッセージ確認
            assertNotNull(e.getMessage(), "レスポンスメッセージが設定されていること");
            assertEquals(expectMsg, e.getMessage(), "レスポンスメッセージが不正");
            System.out.printf("code: %S\nmsg: %S%n", e.getCode(),e.getMessage());

            System.out.println("リクエストパラメータチェック処理終了...");
            throw e;
        }catch (Exception e) {
            System.err.println("リクエストパラメータチェックでエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * テスト用UserInfoオブジェクトを作成
     */
    private UserInfo createTestUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("093540");
        userInfo.setSystemOperationCompanyCode("100001");
        userInfo.setUnitCode("99999");
        userInfo.setAreaCode("0001");
        userInfo.setAreaName("統合テストエリア");
        userInfo.setPositionCode("99");
        return userInfo;
    }
    /**
     * テスト用UserInfoオブジェクトを作成
     */
    private UserInfo createTestUserInfoXY(){
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("XXXYYY");
        userInfo.setSystemOperationCompanyCode("100001");
        userInfo.setUnitCode("99999");
        userInfo.setAreaCode("0001");
        userInfo.setAreaName("統合テストエリア");
        userInfo.setPositionCode("99");
        return userInfo;
    }
    /**
     * APIGatewayProxyRequestEventオブジェクトを作成するヘルパーメソッド
     * @param body リクエストボディ
     * @return 設定済みのAPIGatewayProxyRequestEventオブジェクト
     */
    private APIGatewayProxyRequestEvent createTestRequest(String body) {
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setBody(body);

        // 基本的なヘッダーを設定
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        request.setHeaders(headers);

        // パスパラメータとクエリパラメータを初期化
        request.setPathParameters(new HashMap<>());
        request.setQueryStringParameters(new HashMap<>());

        return request;
    }
}
