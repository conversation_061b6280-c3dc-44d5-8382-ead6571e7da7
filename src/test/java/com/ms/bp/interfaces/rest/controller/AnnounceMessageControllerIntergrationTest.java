package com.ms.bp.interfaces.rest.controller;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.domain.file.model.AnnounceMessage;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.AnnounceMessageResponse;
import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.util.TestDataManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.Map;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;

@ExtendWith(MockitoExtension.class)
@DisplayName("MessageController アナウンスメッセージ取得単元テスト")
public class AnnounceMessageControllerIntergrationTest {

    private static final Logger logger = LoggerFactory.getLogger(AnnounceMessageControllerIntergrationTest.class);
    private AnnounceMessageController announceMessageController;
    private UserInfo testUserInfo;
    private ObjectMapper objectMapper;
    // エクスポート用Excel テストデータ管理器
    private TestDataManager testDataManager;

    // 挿入されたテストデータの追跡情報
    private Map<String, List<Map<String, Object>>> insertedDataTracker;

    /**
     * ケース毎実施前作業
     */
    @BeforeEach
    void setUp() {
        try {
            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");

            // FileControllerを作成
            announceMessageController = new AnnounceMessageController();
            objectMapper = new ObjectMapper();

            System.out.println("AsyncLambdaInvokerのモック設定完了");

        } catch (Exception e) {
            System.err.println("モック設定エラー: " + e.getMessage());
            e.printStackTrace();
            // 初期化に失敗した場合、fileControllerをnullに設定
            announceMessageController = null;
        }

        // テスト用ユーザー情報を設定
        testUserInfo = new UserInfo();
        testUserInfo.setShainCode("TEST001");
        testUserInfo.setSystemOperationCompanyCode("100001");
    }

    /**
     * ケース毎実施後作業
     */
    @AfterEach
    void tearDown() {
        logger.info("=== executeExportTask集成テストクリーンアップ開始 ===");

        // 挿入したテストデータを削除
        if (insertedDataTracker != null) {
            testDataManager.deleteAllTestData(insertedDataTracker);
        }

        logger.info("=== executeExportTask集成テストクリーンアップ完了 ===");
    }

    /**
     * APIGatewayProxyRequestEventを作成するヘルパーメソッド
     */
    private APIGatewayProxyRequestEvent createTestRequest(String requestBody) {
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setBody(requestBody);
        return request;
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_アップロード_正常ケース")
    void testgetAnnounceMessage_アップロード_正常ケース() {
        System.out.println("=== アナウンスメッセージ取得_正常ケース開始 ===");

        String insertDataPath = "announcemessage/insertdata/announcemessage_test_data_1.xlsx";
        testDataManager = new TestDataManager(insertDataPath);
        insertedDataTracker = testDataManager.insertAllTestData();

        // Given: AnnounceMessageController
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: エクスポート履歴用のリクエストデータ
        String requestBody = """
            {
                "msgKbn": "1"
            }
            """;

        APIGatewayProxyRequestEvent request = createTestRequest(requestBody);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");

            if (result.isSuccess()) {
                // 成功時の検証
                assertNotNull(result.getData(), "データが設定されていること");
                assertInstanceOf(AnnounceMessageResponse.class, result.getData(), "AnnounceMessageResponseeが返却されること");

                AnnounceMessageResponse response = (AnnounceMessageResponse) result.getData();
                assertNotNull(response.getMsg(), "メッセージが設定されていること");
                System.out.println("  アナウンスメッセージ: " + response.getMsg());
                System.out.println("✓ アナウンスメッセージ取得成功");
            }

        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_ダウンロード_正常ケース")
    void testgetAnnounceMessage_ダウンロード_正常ケース() {
        System.out.println("=== アナウンスメッセージ取得_正常ケース開始 ===");
        String insertDataPath = "announcemessage/insertdata/announcemessage_test_data_1.xlsx";
        testDataManager = new TestDataManager(insertDataPath);
        insertedDataTracker = testDataManager.insertAllTestData();

        // Given: FileControllerが正常に初期化されていることを確認
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: エクスポート履歴用のリクエストデータ
        String requestBody = """
            {
                "msgKbn": "2"
            }
            """;

        APIGatewayProxyRequestEvent request = createTestRequest(requestBody);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");

            if (result.isSuccess()) {
                // 成功時の検証
                assertNotNull(result.getData(), "データが設定されていること");
                assertInstanceOf(AnnounceMessageResponse.class, result.getData(), "AnnounceMessageResponseeが返却されること");

                AnnounceMessageResponse response = (AnnounceMessageResponse) result.getData();
                assertNotNull(response.getMsg(), "メッセージが設定されていること");
                System.out.println("  アナウンスメッセージ: " + response.getMsg());
                System.out.println("✓ アナウンスメッセージ取得成功");
            }

        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_アップロード_0件_正常ケース")
    void testgetAnnounceMessage_アップロード_0件_正常ケース() {
        System.out.println("=== アナウンスメッセージ取得_正常ケース開始 ===");

        // Given: AnnounceMessageController
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: エクスポート履歴用のリクエストデータ
        String requestBody = """
            {
                "msgKbn": "1"
            }
            """;

        APIGatewayProxyRequestEvent request = createTestRequest(requestBody);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");

            if (result.isSuccess()) {
                // 成功時の検証
                assertNotNull(result.getData(), "データが設定されていること");
                assertInstanceOf(AnnounceMessageResponse.class, result.getData(), "AnnounceMessageResponseeが返却されること");

                AnnounceMessageResponse response = (AnnounceMessageResponse) result.getData();
                assertEquals(response.getMsg(), "");
                System.out.println("  アナウンスメッセージ: " + response.getMsg());
                System.out.println("✓ アナウンスメッセージ取得成功");
            }

        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_ダウンロード_0件_正常ケース")
    void testgetAnnounceMessage_ダウンロード_0件_正常ケース() {
        System.out.println("=== アナウンスメッセージ取得_正常ケース開始 ===");

        // Given: FileControllerが正常に初期化されていることを確認
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: エクスポート履歴用のリクエストデータ
        String requestBody = """
            {
                "msgKbn": "2"
            }
            """;

        APIGatewayProxyRequestEvent request = createTestRequest(requestBody);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: 結果を検証
            assertNotNull(result, "結果がnullではないこと");

            if (result.isSuccess()) {
                // 成功時の検証
                assertNotNull(result.getData(), "データが設定されていること");
                assertInstanceOf(AnnounceMessageResponse.class, result.getData(), "AnnounceMessageResponseeが返却されること");

                AnnounceMessageResponse response = (AnnounceMessageResponse) result.getData();
                assertEquals(response.getMsg(), "");
                System.out.println("  アナウンスメッセージ: " + response.getMsg());
                System.out.println("✓ アナウンスメッセージ取得成功");
            }
        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_必須チェック_異常ケース")
    void testgetAnnounceMessage_必須チェック_空文字列_異常ケース() {
        System.out.println("=== アナウンスメッセージ取得_異常ケース開始 ===");

        // Given: FileControllerが正常に初期化されていることを確認
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: エクスポート履歴用のリクエストデータ
        String requestBody = """
            {
                "msgKbn": ""
            }
            """;

        APIGatewayProxyRequestEvent request = createTestRequest(requestBody);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: エラーレスポンスを検証
            assertNotNull(result, "結果がnullではないこと");
            assertEquals(50001, result.getCode(), "HTTPステータスコードが200であること（エラーレスポンスも200で返却）");
            assertEquals(result.getMsg(), "パラメータ：メッセージ区分、エラー内容：必須チェックエラー");

            System.out.println("✓ nullユーザー情報で適切なエラーレスポンスが返却されました");
            System.out.println("  エラーコード: " + result.getCode());
            System.out.println("  エラーメッセージ: " + result.getMsg());

            // エラーレスポンスであることを確認
            assertFalse(result.isSuccess(), "エラーレスポンスであること");
        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_必須チェック_異常ケース")
    void testgetAnnounceMessage_必須チェック_NULL_異常ケース() {
        System.out.println("=== アナウンスメッセージ取得_異常ケース開始 ===");

        // Given: FileControllerが正常に初期化されていることを確認
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: エクスポート履歴用のリクエストデータ
        String requestBody = """
            {
                "Kbn": ""
            }
            """;

        APIGatewayProxyRequestEvent request = createTestRequest(requestBody);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: エラーレスポンスを検証
            assertNotNull(result, "結果がnullではないこと");
            assertEquals(50001, result.getCode(), "HTTPステータスコードが200であること（エラーレスポンスも200で返却）");
            assertEquals(result.getMsg(), "パラメータ：メッセージ区分、エラー内容：必須チェックエラー");

            System.out.println("✓ nullユーザー情報で適切なエラーレスポンスが返却されました");
            System.out.println("  エラーコード: " + result.getCode());
            System.out.println("  エラーメッセージ: " + result.getMsg());

            // エラーレスポンスであることを確認
            assertFalse(result.isSuccess(), "エラーレスポンスであること");
        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_書式チェック_異常ケース")
    void testgetAnnounceMessage_書式チェック_1と2以外半角数字_異常ケース() {
        System.out.println("=== アナウンスメッセージ取得_異常ケース開始 ===");

        // Given: FileControllerが正常に初期化されていることを確認
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: エクスポート履歴用のリクエストデータ
        String requestBody = """
            {
                "msgKbn": "3"
            }
            """;

        APIGatewayProxyRequestEvent request = createTestRequest(requestBody);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: エラーレスポンスを検証
            assertNotNull(result, "結果がnullではないこと");
            assertEquals(50001, result.getCode(), "HTTPステータスコードが200であること（エラーレスポンスも200で返却）");
            assertEquals(result.getMsg(), "パラメータ：メッセージ区分、エラー内容：書式エラー。\"1\"か\"2\"であることで入力してください。");

            System.out.println("✓ nullユーザー情報で適切なエラーレスポンスが返却されました");
            System.out.println("  エラーコード: " + result.getCode());
            System.out.println("  エラーメッセージ: " + result.getMsg());

            // エラーレスポンスであることを確認
            assertFalse(result.isSuccess(), "エラーレスポンスであること");
        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_書式チェック_異常ケース")
    void testgetAnnounceMessage_書式チェック_1と2以外全角数字_異常ケース() {
        System.out.println("=== アナウンスメッセージ取得_異常ケース開始 ===");

        // Given: FileControllerが正常に初期化されていることを確認
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: エクスポート履歴用のリクエストデータ
        String requestBody = """
            {
                "msgKbn": "３"
            }
            """;

        APIGatewayProxyRequestEvent request = createTestRequest(requestBody);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: エラーレスポンスを検証
            assertNotNull(result, "結果がnullではないこと");
            assertEquals(50001, result.getCode(), "HTTPステータスコードが200であること（エラーレスポンスも200で返却）");
            assertEquals(result.getMsg(), "パラメータ：メッセージ区分、エラー内容：書式エラー。\"1\"か\"2\"であることで入力してください。");

            System.out.println("✓ nullユーザー情報で適切なエラーレスポンスが返却されました");
            System.out.println("  エラーコード: " + result.getCode());
            System.out.println("  エラーメッセージ: " + result.getMsg());

            // エラーレスポンスであることを確認
            assertFalse(result.isSuccess(), "エラーレスポンスであること");
        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_requestBodyチェック_異常ケース")
    void testgetAnnounceMessage_requestBody_空文字列_異常ケース() {
        System.out.println("=== アナウンスメッセージ取得_異常ケース開始 ===");

        // Given: FileControllerが正常に初期化されていることを確認
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        APIGatewayProxyRequestEvent request = createTestRequest("");

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: エラーレスポンスを検証
            assertNotNull(result, "結果がnullではないこと");
            assertEquals(50001, result.getCode(), "HTTPステータスコードが200であること（エラーレスポンスも200で返却）");
            assertEquals(result.getMsg(), "リクエストボディが必要です");

            System.out.println("✓ nullユーザー情報で適切なエラーレスポンスが返却されました");
            System.out.println("  エラーコード: " + result.getCode());
            System.out.println("  エラーメッセージ: " + result.getMsg());

            // エラーレスポンスであることを確認
            assertFalse(result.isSuccess(), "エラーレスポンスであること");
        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    @Test
    @DisplayName("アナウンスメッセージ取得_requestBodyチェック_異常ケース")
    void testgetAnnounceMessage_requestBody_NULL_異常ケース() {
        System.out.println("=== アナウンスメッセージ取得_異常ケース開始 ===");

        // Given: FileControllerが正常に初期化されていることを確認
        if (announceMessageController == null) {
            System.out.println("MessageController初期化失敗のため、テストをスキップします");
            return;
        }

        APIGatewayProxyRequestEvent request = createTestRequest(null);

        try {
            // When: ダウンロードURL生成を実行
            System.out.println("アナウンスメッセージ取得開始...");
            CommonResult<?> result = announceMessageController.getAnnounceMessage(request, testUserInfo);

            // Then: エラーレスポンスを検証
            assertNotNull(result, "結果がnullではないこと");
            assertEquals(50001, result.getCode(), "HTTPステータスコードが200であること（エラーレスポンスも200で返却）");
            assertEquals(result.getMsg(), "リクエストボディが必要です");

            System.out.println("✓ nullユーザー情報で適切なエラーレスポンスが返却されました");
            System.out.println("  エラーコード: " + result.getCode());
            System.out.println("  エラーメッセージ: " + result.getMsg());

            // エラーレスポンスであることを確認
            assertFalse(result.isSuccess(), "エラーレスポンスであること");
        } catch (Exception e) {
            System.err.println("アナウンスメッセージ取得でエラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }
}
