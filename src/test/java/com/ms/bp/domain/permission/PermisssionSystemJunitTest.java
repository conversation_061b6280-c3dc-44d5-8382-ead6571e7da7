package com.ms.bp.domain.permission;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.application.PermissionApplicationService;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponseV2;
import com.ms.bp.util.TestDataManager;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.*;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.assertj.core.api.Assertions;


/**
 * 権限システムJunitテスト
 * 権限システムの全体的な業務フローを検証する統合テストクラス
 *
 * テスト対象：
 * - PermissionService: 権限ドメインサービス
 * - PermissionApplicationService: 権限アプリケーションサービス
 *
 * テストデータ管理：
 * - TestDataManagerを使用してExcelファイルからテストデータを自動挿入・削除
 * - 各テストケース実行前後でデータベース状態を適切に管理
 */
@DisplayName("権限システムJunitテスト")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class PermisssionSystemJunitTest {
    private static final Logger logger = LoggerFactory.getLogger(PermisssionSystemJunitTest.class);

    // テスト対象サービス（実際のインスタンス）
    private PermissionApplicationService permissionApplicationService;

    // テストデータ管理
    private TestDataManager testDataManager;
    private Map<String, List<Map<String, Object>>> insertedDataTracker;

    @BeforeEach
    void setUp() {
        logger.info("=== 権限システムテストセットアップ開始 ===");

        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);

            // AWS設定を環境変数で設定してリソース初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");

            // テスト対象サービスの初期化
            permissionApplicationService = new PermissionApplicationService();

            logger.info("=== 権限システムテストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("権限システムテストセットアップエラー: {}", e.getMessage(), e);
            e.printStackTrace();
            // 初期化に失敗した場合、permissionApplicationServiceをnullに設定
            permissionApplicationService = null;
        }
    }

    @AfterEach
    void tearDown() {
        logger.info("=== 権限システムテストクリーンアップ開始 ===");

        // 挿入したテストデータを削除
        if (insertedDataTracker != null) {
            testDataManager.deleteAllTestData(insertedDataTracker);
        }
        logger.info("=== 権限システムテストクリーンアップ完了 ===");
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:61
     *              ・システム管理者フラグ 1:システム管理者
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ 1:システム管理者
     *              ・役職区分:61
     *              ・権限リスト:全権限
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト1")
    void testPermissionSystem_test1() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST001","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test1/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test1/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:51
     *              ・システム管理者フラグ 1:システム管理者
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ 1:システム管理者
     *              ・役職区分:51
     *              ・権限リスト:全権限
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト2")
    void testPermissionSystem_test2() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST051","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test2/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test2/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:41
     *              ・システム管理者フラグ 1:システム管理者
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ 1:システム管理者
     *              ・役職区分:41
     *              ・権限リスト:全権限
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト3")
    void testPermissionSystem_test3() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST041","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test3/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test3/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }


    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:90
     *              ・システム管理者フラグ 1:システム管理者
     * 【期待結果】：
     *              ・画面表示可否 1:事業計画システムの画面表示不可
     *              ・システム管理者フラグ:null
     *              ・roleリスト:null
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト4")
    void testPermissionSystem_test4() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST002","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test4/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test4/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:90
     *              ・システム管理者フラグ 0:システム管理者以外
     * 【期待結果】：
     *              ・画面表示可否 1:事業計画システムの画面表示不可
     *              ・システム管理者フラグ:null
     *              ・roleリスト:null
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト4_1")
    void testPermissionSystem_test4_1() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST002","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test4-1/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test4-1/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:90
     *              ・システム管理者フラグ 0:システム管理者以外
     * 【期待結果】：
     *              ・画面表示可否 1:事業計画システムの画面表示不可
     *              ・システム管理者フラグ:0
     *              ・roleリスト:[]
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト4_2")
    void testPermissionSystem_test4_2() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST002","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test4-2/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test4-2/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }


    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・システム管理者フラグ 0:非システム管理者
     *              ・事業計画システム専用兼務マスタ:役職区分 IN (41,51,61)
     *              ・社員マスタ:役職区分 41
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ:0
     *              ・主職務と兼務の重複データを削除
     *              ・roleリスト:41、51と61三つのroleが存在する
     *              ・権限情報リスト
     *                  ４１：全エリアに権限を持つ
     *                  ５１，６１：自エリアに権限を持つ
     *              ・エリア情報リスト
     *                  ４１；全エリア
     *                  ５１、６１；自エリアのみ
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト5")
    void testPermissionSystem_test5() {
            // テスト用ユーザー情報を作成
            UserInfo testUserInfo = createTestUserInfo("TST041","200001","0","41","0000","00000");

            // excelのテストデータのファイルパース
            String inputUrl = "permissionJunitData/test5/input_data/permission_test_data.xlsx";

            // 期待結果のファイルパース
            String expectUrl = "permissionJunitData/test5/expect_data/response.json";

            // テストを実施
            execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・システム管理者フラグ 0:非システム管理者
     *              ・社員マスタ:役職区分 51
     *              ・権限コードの重複削除した結果、3桁目に"H"（本社相当）を持つ権限コードと、"A"（自エリア相当）を持つ権限コードが混在する場合、"A"を持つ権限コードを削除する
     *              ・個人別権限データによって.許可フラグが許可
     *              ・共通権限
     *                  DLA002
     *                  DLA003
     *                  DLA004
     *                  ULA003
     *                  ULA004
     *              ・個人別権限
     *                  DLH001	使用禁止区分:0許可
     *                  DLH003	使用禁止区分:0許可
     *                  DLH004	使用禁止区分:1禁止
     *                  ULA001	使用禁止区分:0許可
     *                  ULA002	使用禁止区分:1禁止
     *                  ULH003	使用禁止区分:1禁止
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ:0
     *              ・roleリスト:51
     *              ・権限情報リスト
     *                  DLH001
     *                  DLA002
     *                  DLH003
     *                  DLA004
     *                  ULA001
     *                  ULA003
     *                  ULA004
     *              ・エリア情報リスト
     *                  全エリア
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト6")
    void testPermissionSystem_test6() {
        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST002","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test6/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test6/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・システム管理者フラグ 0:非システム管理者
     *              ・事業計画システム専用兼務マスタ:役職区分 IN (41,51,61)
     *              ・社員マスタ:役職区分 41
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ:0
     *              ・主職務と兼務の重複データを削除
     *              ・roleリスト:41、51と61三つのroleが存在する
     *              役職区分判定要否:
     *                  41:1
     *                  51:0
     *                  61:1
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト7")
    void testPermissionSystem_test7() {
        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST041","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test7/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test7/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・システム管理者フラグ 0:非システム管理者
     *              ・事業計画システム専用兼務マスタ:役職区分 IN (41,51,61)
     *              ・社員マスタ:役職区分 61
     *              ・権限コードの3桁目に"A"が1件も含まれない
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ:0
     *              ・roleリスト:61が存在する
     *              ・役職区分判定要否:
     *                  61:0
     *              ・権限情報リスト
     *                  DLH002
     *                  DLH003
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト8")
    void testPermissionSystem_test8() {
        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST011","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test8/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test8/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }

    /**
     * 権限システム_正常系（画面側の権限リストを取得）
     * 【テスト条件】：
     *              ・システム管理者フラグ 0:非システム管理者
     *              ・事業計画システム専用兼務マスタ:役職区分 IN (41,51,61)
     *              ・社員マスタ:役職区分 61
     *              ・権限コード4-6桁目が"002","003"を含んでいない
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ:0
     *              ・roleリスト:61が存在する
     *              役職区分判定要否:
     *                  61:0
     *              ・権限情報リスト:
     *                  DLA001
     *                  DLA004
     *              ・エリア情報:
     *                  自分のエリアのみ
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト9")
    void testPermissionSystem_test9() {
        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST011","200001","0","41","0000","00000");

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test9/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test9/expect_data/response.json";

        // テストを実施
        execPermissionJunit(testUserInfo,inputUrl, expectUrl);
    }


    /**
     * 権限システム_正常系（バックエンド側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:41
     *              ・システム管理者フラグ 1:システム管理者
     *              ・操作区分:2 （ダウンロード）
     *              ・エリアコード:1600
     *              ・ユニットコード:U0041
     *              ・グループコード:G041
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ 1:システム管理者
     *              ・役職区分:41
     *              ・権限リスト:（ダウンロード）権限
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト10")
    void testPermissionSystem_test10() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST041","200001","0","41","0000","00000");

        String unitCode = "U0041";
        String positionCode = "41";
        String areaCode = "1600";
        String groupCode = "G041";
        String sysrtemAdminFlag = "1";
        String operation = "2";

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test10/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test10/expect_data/response.json";

        // テストを実施
        execPermissionJunitBackend(testUserInfo,inputUrl, expectUrl, unitCode, positionCode, areaCode, groupCode, sysrtemAdminFlag, operation);
    }

    /**
     * 権限システム_正常系（バックエンド側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:41
     *              ・システム管理者フラグ 0:システム管理者
     *              ・操作区分:1 （アップロード）
     *              ・エリアコード:1600
     *              ・ユニットコード:U0041
     *              ・グループコード:G041
     * 【期待結果】：
     *              ・画面表示可否 0:事業計画システムの画面表示可
     *              ・システム管理者フラグ 0:システム管理者
     *              ・役職区分:41
     *              ・ロールリスト:41
     *              ・権限リスト:（ダウンロード）権限
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト11")
    void testPermissionSystem_test11() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST041","200001","0","41","0000","00000");

        String unitCode = "U0041";
        String positionCode = "41";
        String areaCode = "1600";
        String groupCode = "G041";
        String sysrtemAdminFlag = "0";
        String operation = "1";

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test11/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test11/expect_data/response.json";

        // テストを実施
        execPermissionJunitBackend(testUserInfo,inputUrl, expectUrl, unitCode, positionCode, areaCode, groupCode, sysrtemAdminFlag, operation);
    }

    /**
     * 権限システム_正常系（バックエンド側の権限リストを取得）
     * 【テスト条件】：
     *              ・役職区分:41
     *              ・システム管理者フラグ 0:システム管理者
     *              ・操作区分:1 （アップロード）
     *              ・エリアコード:1600
     *              ・ユニットコード:U0041
     *              ・グループコード:G041
     * 【期待結果】：
     *              ・画面表示可否 １:事業計画システムの画面表示不可
     *              ・システム管理者フラグ 0:システム管理者
     *              ・役職区分:41
     *              ・ロールリスト:なし
     */
    @Test
    @DisplayName("権限システムテスト_正常系テスト12")
    void testPermissionSystem_test12() {

        // テスト用ユーザー情報を作成
        UserInfo testUserInfo = createTestUserInfo("TST041","200001","0","41","0000","00000");

        String unitCode = "U0041";
        String positionCode = "41";
        String areaCode = "1600";
        String groupCode = "G041";
        String sysrtemAdminFlag = "0";
        String operation = "1";

        // excelのテストデータのファイルパース
        String inputUrl = "permissionJunitData/test12/input_data/permission_test_data.xlsx";

        // 期待結果のファイルパース
        String expectUrl = "permissionJunitData/test12/expect_data/response.json";

        // テストを実施
        execPermissionJunitBackend(testUserInfo,inputUrl, expectUrl, unitCode, positionCode, areaCode, groupCode, sysrtemAdminFlag, operation);
    }


    // ==================== プライベートヘルパーメソッド ====================

    /**
     * テストデータをデータベースに登録する
     */
    private void insertTestDataFromExcel(String testDataUrl){
        // 見通し・計画＜エリア＞エクスポート用Excel テストデータ管理器の初期化
        testDataManager = new TestDataManager(testDataUrl);
        // Excel からテストデータを読み込んでデータベースに挿入
        insertedDataTracker = testDataManager.insertAllTestData();

    }

    /**
     * テスト用UserInfoオブジェクトを作成
     */
    private UserInfo createTestUserInfo(String shainCode, String SystemOperationCompanyCode, String positionSpecialCheck, String positionCode, String groupCode, String unitCode) {
        UserInfo userInfo = new UserInfo();
        AreaInfo area1 = new AreaInfo("7771", "単元テストエリア7771");
        AreaInfo area2 = new AreaInfo("7772", "単元テストエリア7772");
        userInfo.setAreaInfos(List.of(area1, area2));
        userInfo.setAreaCode("0700");
        userInfo.setAreaName("単元テストエリア0700");

        userInfo.setShainCode(shainCode);
        userInfo.setSystemOperationCompanyCode(SystemOperationCompanyCode);
        userInfo.setPositionSpecialCheck(positionSpecialCheck);
        userInfo.setPositionCode(positionCode);
        userInfo.setGroupCode(groupCode);
        userInfo.setUnitCode(unitCode);
        return userInfo;
    }

    /**
     * 具体的なテストプロセス
     */
    private void execPermissionJunit(UserInfo testUserInfo, String inputUrl, String expectUrl){
        logger.info("=== ユーザー権限一覧取得処理　テスト開始 ===");
        if (permissionApplicationService == null) {
            System.out.println("PermissionController初期化失敗のため、テストをスキップします");
            return;
        }
        try {
            // Given: テスト用データを準備
            // excelからテストデータをデータベースに登録する
            insertTestDataFromExcel(inputUrl);
            // 期待結果を取得する
            UserPermissionsResponseV2 expectResponse = getExpectResponse(expectUrl);

            // When: 権限を取得
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                        testUserInfo, null, null, null, null, null, null);

            // Then: 権限が返されることを検証
            compareResponse(expectResponse,response);

            logger.info("✅ ===ユーザー権限一覧取得処理　テスト完了 ===");
        } catch (Exception e) {
            logger.error("ユーザー権限一覧取得処理　テストエラー: {}", e.getMessage(), e);
            Assertions.fail("ユーザー権限一覧取得処理テストに失敗しました: " + e.getMessage());
        }
    }

    private void execPermissionJunitBackend(UserInfo testUserInfo, String inputUrl, String expectUrl, String unitCode, String positionCode, String areaCode, String groupCode, String sysrtemAdminFlag, String operation){
        logger.info("=== ユーザー権限一覧取得処理　テスト開始 ===");
        if (permissionApplicationService == null) {
            System.out.println("PermissionController初期化失敗のため、テストをスキップします");
            return;
        }
        try {
            // Given: テスト用データを準備
            // excelからテストデータをデータベースに登録する
            insertTestDataFromExcel(inputUrl);
            // 期待結果を取得する
            UserPermissionsResponseV2 expectResponse = getExpectResponse(expectUrl);

            // When: 権限を取得
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    testUserInfo, unitCode, positionCode, areaCode, groupCode, sysrtemAdminFlag, operation);

            // Then: 権限が返されることを検証
            compareResponse(expectResponse,response);

            logger.info("✅ ===ユーザー権限一覧取得処理　テスト完了 ===");
        } catch (Exception e) {
            logger.error("ユーザー権限一覧取得処理　テストエラー: {}", e.getMessage(), e);
            Assertions.fail("ユーザー権限一覧取得処理テストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 期待結果を取得する
     */
    private UserPermissionsResponseV2 getExpectResponse (String expectDataUrl){
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("testdata/" + expectDataUrl);){
            ObjectMapper objectMapper = new ObjectMapper();

            // expectデータをjsonファイルから取得する
            return objectMapper.readValue(
                    inputStream,  // JSON ファイルパース
                    UserPermissionsResponseV2.class
            );
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("期待結果取得処理でエラーが発生しました エラー: {}", e.getMessage(), e);
            return new UserPermissionsResponseV2();
        }
    }

    /**
     * 実際の結果と期待結果を比較する
     */
    private void compareResponse(UserPermissionsResponseV2 expect, UserPermissionsResponseV2 result){

        Assertions.assertThat(result)
                .as("権限レスポンスが取得できること")
                .isNotNull()
                .as("システム運用企業コードが同じかどうか")
                .hasFieldOrPropertyWithValue("systemOperationCompanyCode", expect.getSystemOperationCompanyCode())
                .as("社員コードが同じかどうか")
                .hasFieldOrPropertyWithValue("shainCode", expect.getShainCode())
                .as("画面表示可否が同じかどうか")
                .hasFieldOrPropertyWithValue("screenDisplayFlag", expect.getScreenDisplayFlag())
                .as("システム管理者フラグが同じかどうか")
                .hasFieldOrPropertyWithValue("systemAdminFlag", expect.getSystemAdminFlag());

        // 実際のロールリストと期待のロールリストのサイズを比較
        if(!Objects.isNull(result.getRoleList()) && !Objects.isNull(expect.getRoleList())){
            Assertions.assertThat(result.getRoleList().size())
                    .as("実際のロールリストと期待のサイズが同じかどうか")
                    .isEqualTo(expect.getRoleList().size());

            // 実際のロールリストを取得
            List<UserPermissionsResponseV2.RoleInfo> sortedResultList = result.getRoleList().stream()
                    .sorted(
                            Comparator.comparing(UserPermissionsResponseV2.RoleInfo::getAreaCode)
                                    .thenComparing(UserPermissionsResponseV2.RoleInfo::getUnitCode)
                                    .thenComparing(UserPermissionsResponseV2.RoleInfo::getGroupCode)
                                    .thenComparing(UserPermissionsResponseV2.RoleInfo::getPositionCode)
                    )
                    .toList();
            sortedResultList.forEach(roleInfo -> {
                List<UserPermissionsResponseV2.PermissionInfo> permissionList = roleInfo.getPermissionList();
                List<UserPermissionsResponseV2.PermissionInfo> sortedPermissionList = permissionList.stream()
                        .sorted(
                                Comparator.comparing(UserPermissionsResponseV2.PermissionInfo::getPermissionCode)
                        ) .toList();
                roleInfo.setPermissionList(sortedPermissionList);
            });
            // 期待のロールリストを取得
            List<UserPermissionsResponseV2.RoleInfo> sortedExpectList = expect.getRoleList().stream()
                    .sorted(
                            Comparator.comparing(UserPermissionsResponseV2.RoleInfo::getAreaCode)
                                    .thenComparing(UserPermissionsResponseV2.RoleInfo::getUnitCode)
                                    .thenComparing(UserPermissionsResponseV2.RoleInfo::getGroupCode)
                                    .thenComparing(UserPermissionsResponseV2.RoleInfo::getPositionCode)
                    )
                    .toList();
            sortedExpectList.forEach(roleInfo -> {
                List<UserPermissionsResponseV2.PermissionInfo> permissionList = roleInfo.getPermissionList();
                List<UserPermissionsResponseV2.PermissionInfo> sortedPermissionList = permissionList.stream()
                        .sorted(
                                Comparator.comparing(UserPermissionsResponseV2.PermissionInfo::getPermissionCode)
                        ) .toList();
                roleInfo.setPermissionList(sortedPermissionList);
            });

            Assertions.assertThat(sortedResultList)
                    .as("ロールリストが同じかどうか")
                    .isEqualTo(sortedExpectList);

        }else {
            Assertions.assertThat(result.getRoleList())
                    .as("実際に取得したロールリストはnullかどうか")
                    .isEqualTo(null);
        }


    }

}
