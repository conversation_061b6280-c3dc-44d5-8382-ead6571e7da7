# Test environment configuration
# AWS configuration
aws.region=ap-northeast-1
aws.s3.bucket.name=ms-bp-files-prod-standard

# Database configuration
# Parameter Store settings (priority)
db.use.parameter.store=true
db.parameter.prefix=/ms-bp/prod/standard/db

# SSH Tunnel configuration for connecting to AWS dev database
ssh.tunnel.enabled=false

# Logging configuration
aws.lambda.log.format=TEXT
aws.lambda.log.level=ERROR
