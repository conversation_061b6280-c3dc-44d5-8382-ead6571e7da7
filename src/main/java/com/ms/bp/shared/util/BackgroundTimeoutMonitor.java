package com.ms.bp.shared.util;

import com.amazonaws.services.lambda.runtime.Context;
import org.apache.commons.lang3.ThreadUtils;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * Lambda実行時間後台監視クラス
 * Lambda実行時間の監視とタイムアウト接近検出を行う
 */
public class BackgroundTimeoutMonitor {

    private static final Logger logger = LoggerFactory.getLogger(BackgroundTimeoutMonitor.class);

    /** デフォルトタイムアウト緩衝時間（30秒） - Duration型で時間を表現 */
    private static final Duration DEFAULT_BUFFER_TIME = Duration.ofSeconds(30);

    /** 監視間隔（30秒） - Lambda残り時間のチェック頻度 */
    private static final Duration MONITORING_INTERVAL = Duration.ofSeconds(30);

    /** Lambda実行コンテキスト */
    private final Context lambdaContext;

    /** ジョブID（ログとコールバックで使用） */
    private final String jobId;

    /** タイムアウト緩衝時間（ミリ秒） */
    private final long bufferTimeMs;

    /** タイムアウト時の処理を定義するコールバック */
    private final Consumer<String> timeoutCallback;

    /** タイムアウト検出フラグ（スレッドセーフ） */
    private final AtomicBoolean timeoutDetected = new AtomicBoolean(false);

    /** 監視スレッド（volatileで可視性保証） */
    private volatile Thread monitoringThread;

    /**
     * デフォルト設定のコンストラクタ
     * 緩衝時間は30秒に設定される
     */
    public BackgroundTimeoutMonitor(Context lambdaContext, String jobId, Consumer<String> timeoutCallback) {
        this(lambdaContext, jobId, timeoutCallback, DEFAULT_BUFFER_TIME.toMillis());
    }

    /**
     * 詳細設定可能なコンストラクタ
     *
     * @param lambdaContext Lambda実行コンテキスト（必須）
     * @param jobId ジョブID（必須、空文字不可）
     * @param timeoutCallback タイムアウト時の処理（必須）
     * @param bufferTimeMs カスタム緩衝時間（0以上）
     */
    public BackgroundTimeoutMonitor(Context lambdaContext, String jobId, Consumer<String> timeoutCallback, long bufferTimeMs) {
        // Apache Commons Langのバリデーション機能で入力チェック
        // nullチェック、空文字チェック、数値範囲チェックを簡潔に実装
        Validate.notNull(lambdaContext, "Lambda contextは必須です");
        Validate.notBlank(jobId, "ジョブIDは必須です");
        Validate.notNull(timeoutCallback, "タイムアウト回调函数は必須です");
        Validate.isTrue(bufferTimeMs >= 0, "緩衝時間は0以上である必要があります");

        this.lambdaContext = lambdaContext;
        this.jobId = jobId;
        this.timeoutCallback = timeoutCallback;
        this.bufferTimeMs = bufferTimeMs;
    }

    /**
     * 監視スレッドを開始する（synchronized で同期化）
     */
    public synchronized void startMonitoring() {
        // 既存スレッドの生存チェック
        if (monitoringThread != null && monitoringThread.isAlive()) {
            return;
        }

        // デーモンスレッドを作成
        monitoringThread = new Thread(() -> {
            // 監視ループ
            while (!Thread.currentThread().isInterrupted() && !timeoutDetected.get()) {
                // Lambda関数の残り実行時間を取得
                var remainingTimeMs = lambdaContext.getRemainingTimeInMillis();

                // タイムアウト判定とフラグ設定を一つの操作で実行
                if (remainingTimeMs <= bufferTimeMs && timeoutDetected.compareAndSet(false, true)) {
                    logger.warn("タイムアウト接近を検出: jobId={}, 残り時間={}ms", jobId, remainingTimeMs);

                    try {
                        // コールバック実行（DB更新、ファイル削除など）
                        timeoutCallback.accept(jobId);
                    } catch (Exception e) {
                        logger.error("タイムアウト処理でエラー発生: jobId={}", jobId, e);
                    }
                    break; // タイムアウト処理後は監視終了
                }

                try {
                    // 指定時間待機
                    ThreadUtils.sleep(MONITORING_INTERVAL);
                } catch (InterruptedException e) {
                    // 中断シグナルを受信したらループを終了
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }, "TimeoutMonitor-" + jobId);

        // デーモンスレッドとして設定
        monitoringThread.setDaemon(true);

        // スレッドを開始
        monitoringThread.start();
        logger.info("監視開始: jobId={}", jobId);
    }

    /**
     * タイムアウトが検出されたかを確認
     *
     * @return タイムアウト検出済みの場合true
     */
    public boolean isTimeoutDetected() {
        return timeoutDetected.get();
    }

    /**
     * 監視スレッドを停止してリソースをクリーンアップ
     */
    public void cleanup() throws InterruptedException {
        if (monitoringThread != null) {
            // 中断シグナルを送信
            monitoringThread.interrupt();

            // 最大5秒待機
            ThreadUtils.join(monitoringThread, Duration.ofSeconds(5));
        }
    }
}