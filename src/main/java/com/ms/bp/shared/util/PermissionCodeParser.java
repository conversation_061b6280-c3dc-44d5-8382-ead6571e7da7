package com.ms.bp.shared.util;

import com.ms.bp.shared.common.constants.BusinessConstants;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Map;

/**
 * 権限コード解析ユーティリティクラス
 * 権限コードの各桁から業務情報を抽出する共通ロジックを提供
 *
 * 権限コード構造：
 * - 1-2桁目：操作区分（UL=アップロード、DL=ダウンロード）
 * - 3桁目：対象所属区分（H=本社、A=エリア）
 * - 4-6桁目：ファイル種類コード（001-004）
 */
public final class PermissionCodeParser {

    // 権限コード解析用定数
    private static final String DOWNLOAD_PREFIX = "DL";
    private static final String UPLOAD_PREFIX = "UL";
    private static final char HEAD_OFFICE_MARKER = 'H';
    private static final char AREA_MARKER = 'A';
    
    // ファイルタイプマッピング
    private static final Map<String, String> FILE_TYPE_MAPPING = Map.of(
            "001", BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE,
            "002", BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE,
            "003", BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE,
            "004", BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE
    );

    // ユーティリティクラスのためコンストラクタを非公開
    private PermissionCodeParser() {
        throw new UnsupportedOperationException("ユーティリティクラスのためインスタンス化できません");
    }

    /**
     * 権限コード解析結果を保持する値オブジェクト
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ParsedPermissionCode {
        /**
         * 元の権限コード
         */
        private String permissionCode;
        
        /**
         * 操作区分（BusinessConstants値）
         */
        private String operationDivision;
        
        /**
         * エリアパターン（BusinessConstants値）
         */
        private String areaPattern;
        
        /**
         * ファイル種類コード（BusinessConstants値）
         */
        private String fileTypeCode;

    }

    /**
     * 権限コードを解析し、各構成要素を抽出する
     * 
     * @param permissionCode 権限コード（6桁以上の文字列）
     * @return 解析結果オブジェクト
     * @throws IllegalArgumentException 権限コードが無効な場合
     */
    public static ParsedPermissionCode parse(String permissionCode) {
        if (permissionCode == null || permissionCode.length() < 6) {
            throw new IllegalArgumentException("権限コードは6桁以上である必要があります: " + permissionCode);
        }

        // 1-2桁目: 操作区分
        String operation = permissionCode.substring(0, 2);
        String operationDivision = switch (operation) {
            case DOWNLOAD_PREFIX -> BusinessConstants.OPERATION_DOWNLOAD_CODE;
            case UPLOAD_PREFIX -> BusinessConstants.OPERATION_UPLOAD_CODE;
            default -> "";
        };

        // 3桁目: 対象所属区分
        char areaPatternChar = permissionCode.charAt(2);
        String areaPattern = switch (areaPatternChar) {
            case HEAD_OFFICE_MARKER -> BusinessConstants.AFFILIATION_HEAD_OFFICE;
            case AREA_MARKER -> BusinessConstants.AFFILIATION_AREA;
            default -> "";
        };

        // 4-6桁目: ファイル種類コード
        String fileTypeCode = permissionCode.substring(3, 6);
        String fileType = FILE_TYPE_MAPPING.getOrDefault(fileTypeCode, "");


        return ParsedPermissionCode.builder()
            .permissionCode(permissionCode)
            .operationDivision(operationDivision)
            .areaPattern(areaPattern)
            .fileTypeCode(fileType)
            .build();
    }
}
