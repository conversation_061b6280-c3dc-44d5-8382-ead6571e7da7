package com.ms.bp.shared.util;

import com.ms.bp.application.factory.DomainServiceFactory;
import com.ms.bp.shared.common.db.AuroraConnectionManager;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.function.Function;

/**
 * Lambda環境下のリソース管理ユーティリティ
 * データベース接続とドメインサービスファクトリの統合管理を行う
 */
public class LambdaResourceManager {
    private static final Logger logger = LoggerFactory.getLogger(LambdaResourceManager.class);

    /**
     * Domain層向けJDBC操作用の函数式インターフェース
     * DomainServiceFactoryに依存せず、直接JdbcTemplateを使用する操作を定義
     * @param <T> 戻り値の型
     */
    @FunctionalInterface
    public interface JdbcOperation<T> {
        /**
         * JdbcTemplateを使用してデータベース操作を実行
         * @param jdbcTemplate データベース操作用のJdbcTemplate
         * @return 操作結果
         * @throws SQLException データベースエラー
         */
        T execute(JdbcTemplate jdbcTemplate) throws SQLException;
    }

    
    /**
     * トランザクション付きでデータベースリソースとドメインサービスファクトリを使用して処理を実行
     * @param operation 実行する処理
     * @param <T> 戻り値の型
     * @return 処理結果
     * @throws RuntimeException データベースエラーまたは処理エラー
     */
    public static <T> T executeWithTransaction(Function<DomainServiceFactory, T> operation) {
        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {
            
            // トランザクション開始
            connection.setAutoCommit(false);
            
            // ドメインサービスファクトリを作成
            DomainServiceFactory serviceFactory = new DomainServiceFactory(jdbcTemplate);
            
            try {
                logger.debug("トランザクション付きLambda処理を開始します");
                
                // 処理を実行
                T result = operation.apply(serviceFactory);
                
                // コミット
                connection.commit();
                
                logger.debug("トランザクション付きLambda処理が正常に完了しました");
                return result;
                
            } catch (Exception e) {
                // ロールバック
                try {
                    connection.rollback();
                    logger.warn("トランザクションをロールバックしました");
                } catch (SQLException rollbackException) {
                    logger.error("ロールバック中にエラーが発生しました", rollbackException);
                }
                throw e;
                
            } finally {
                // ファクトリのリソースをクリーンアップ
                serviceFactory.cleanup();
            }
            
        } catch (SQLException e) {
            logger.error("データベース接続エラーが発生しました", e);
            throw new RuntimeException("データベース接続に失敗しました", e);
        } catch (Exception e) {
            logger.error("トランザクション付きLambda処理中にエラーが発生しました", e);
            throw new RuntimeException("処理に失敗しました", e);
        }
    }

    /**
     * トランザクション付きでデータベースリソースとドメインサービスファクトリを使用して処理を実行
     * @param operation 実行する処理
     * @param <T> 戻り値の型
     * @return 処理結果
     * @throws RuntimeException データベースエラーまたは処理エラー
     */
    public static <T> T executeWithTransactionEmergency(Function<DomainServiceFactory, T> operation) {
        try (Connection connection = AuroraConnectionManager.getConnectionEmergency();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            // トランザクション開始
            connection.setAutoCommit(false);

            // ドメインサービスファクトリを作成
            DomainServiceFactory serviceFactory = new DomainServiceFactory(jdbcTemplate);

            try {
                logger.debug("トランザクション付きLambda処理を開始します");

                // 処理を実行
                T result = operation.apply(serviceFactory);

                // コミット
                connection.commit();

                logger.debug("トランザクション付きLambda処理が正常に完了しました");
                return result;

            } catch (Exception e) {
                // ロールバック
                try {
                    connection.rollback();
                    logger.warn("トランザクションをロールバックしました");
                } catch (SQLException rollbackException) {
                    logger.error("ロールバック中にエラーが発生しました", rollbackException);
                }
                throw e;

            } finally {
                // ファクトリのリソースをクリーンアップ
                serviceFactory.cleanup();
            }

        } catch (SQLException e) {
            logger.error("データベース接続エラーが発生しました", e);
            throw new RuntimeException("データベース接続に失敗しました", e);
        } catch (Exception e) {
            logger.error("トランザクション付きLambda処理中にエラーが発生しました", e);
            throw new RuntimeException("処理に失敗しました", e);
        }
    }
    
    /**
     * 読み取り専用でデータベースリソースとドメインサービスファクトリを使用して処理を実行
     * @param operation 実行する処理
     * @param <T> 戻り値の型
     * @return 処理結果
     * @throws RuntimeException データベースエラーまたは処理エラー
     */
    public static <T> T executeReadOnly(Function<DomainServiceFactory, T> operation) {
        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {
            
            // 読み取り専用に設定
            connection.setReadOnly(true);
            
            // ドメインサービスファクトリを作成
            DomainServiceFactory serviceFactory = new DomainServiceFactory(jdbcTemplate);
            
            try {
                logger.debug("読み取り専用Lambda処理を開始します");
                
                // 処理を実行
                T result = operation.apply(serviceFactory);
                
                logger.debug("読み取り専用Lambda処理が正常に完了しました");
                return result;
                
            } finally {
                // ファクトリのリソースをクリーンアップ
                serviceFactory.cleanup();
            }
            
        } catch (SQLException e) {
            logger.error("データベース接続エラーが発生しました", e);
            throw new RuntimeException("データベース接続に失敗しました", e);
        } catch (Exception e) {
            logger.error("読み取り専用Lambda処理中にエラーが発生しました", e);
            throw new RuntimeException("処理に失敗しました", e);
        }
    }

    // ==================== Domain層向けJDBC操作メソッド ====================

    /**
     * Domain層向け読み取り専用データベース操作
     * DomainServiceFactoryに依存せず、直接JdbcTemplateを提供
     * DDD分層アーキテクチャに準拠し、Domain層からの使用に適している
     *
     * @param operation 実行するJDBC操作
     * @param <T> 戻り値の型
     * @return 操作結果
     * @throws RuntimeException データベースエラーまたは処理エラー
     */
    public static <T> T executeWithJdbcTemplateReadOnly(JdbcOperation<T> operation) {
        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            // 読み取り専用に設定
            connection.setReadOnly(true);

            logger.debug("Domain層読み取り専用データベース操作を開始します");

            // 操作を実行
            T result = operation.execute(jdbcTemplate);

            logger.debug("Domain層読み取り専用データベース操作が正常に完了しました");
            return result;

        } catch (SQLException e) {
            logger.error("Domain層データベース接続エラーが発生しました", e);
            throw new RuntimeException("データベース接続に失敗しました", e);
        } catch (Exception e) {
            logger.error("Domain層読み取り専用処理中にエラーが発生しました", e);
            throw new RuntimeException("処理に失敗しました", e);
        }
    }

    /**
     * Domain層向けトランザクション付きデータベース操作
     * DomainServiceFactoryに依存せず、直接JdbcTemplateを提供
     * DDD分層アーキテクチャに準拠し、Domain層からの使用に適している
     *
     * @param operation 実行するJDBC操作
     * @param <T> 戻り値の型
     * @return 操作結果
     * @throws RuntimeException データベースエラーまたは処理エラー
     */
    public static <T> T executeWithJdbcTemplateTransaction(JdbcOperation<T> operation) {
        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            // トランザクション開始
            connection.setAutoCommit(false);

            try {
                logger.debug("Domain層トランザクション付きデータベース操作を開始します");

                // 操作を実行
                T result = operation.execute(jdbcTemplate);

                // コミット
                connection.commit();

                logger.debug("Domain層トランザクション付きデータベース操作が正常に完了しました");
                return result;

            } catch (Exception e) {
                // ロールバック
                try {
                    connection.rollback();
                    logger.warn("Domain層トランザクションをロールバックしました");
                } catch (SQLException rollbackException) {
                    logger.error("Domain層ロールバック中にエラーが発生しました", rollbackException);
                }
                throw e;
            }

        } catch (SQLException e) {
            logger.error("Domain層データベース接続エラーが発生しました", e);
            throw new RuntimeException("データベース接続に失敗しました", e);
        } catch (Exception e) {
            logger.error("Domain層トランザクション付き処理中にエラーが発生しました", e);
            throw new RuntimeException("処理に失敗しました", e);
        }
    }

}
