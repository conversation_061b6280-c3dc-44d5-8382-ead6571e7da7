package com.ms.bp.shared.util;

import com.ms.bp.shared.common.constants.BusinessConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * 一時ファイルクリーンアップユーティリティ
 */
public class TempFileCleanupUtil {
    private static final Logger logger = LoggerFactory.getLogger(TempFileCleanupUtil.class);

    /**
     * エクスポート処理で生成された一時ファイルを安全にクリーンアップ
     */
    public static void cleanupExportTempFiles(List<Path> csvFiles, Path zipFile) {
        if (isTestEnvironment()) {
            logger.info("テスト環境のため削除をスキップします");
            return;
        }

        // CSVファイルを削除
        Stream.ofNullable(csvFiles)
                .flatMap(List::stream)
                .forEach(csvFile -> deleteFile(csvFile, "CSV"));

        // ZIPファイルと一時ディレクトリを削除
        if (zipFile != null) {
            deleteFile(zipFile, "ZIP");
        }

        // 親ディレクトリを削除
        Stream.concat(Stream.ofNullable(csvFiles).flatMap(List::stream).map(Path::getParent),
                        Stream.ofNullable(zipFile).map(Path::getParent))
                .filter(Objects::nonNull)
                .distinct()  // 重複を除去
                .forEach(dir -> {
                    deleteFile(dir, "親ディレクトリ");
        });
    }

    /**
     * Lambda超時時の一時ディレクトリクリーンアップ（Commons IO版）
     */
    public static void cleanupOldestTempDirectoriesOnTimeout(String jobId) {
        if (isTestEnvironment()) {
            return;
        }

        var tempDir = new File(System.getProperty("java.io.tmpdir"));

        // 各プレフィックスの最古ディレクトリを削除
        List.of("export_", "zip_temp").forEach(prefix -> {
            var dirs = tempDir.listFiles((dir, name) ->
                    new File(dir, name).isDirectory() && name.startsWith(prefix));

            if (dirs != null && dirs.length > 0) {
                // 最も古いディレクトリを削除
                Arrays.stream(dirs)
                        .min(Comparator.comparing(File::lastModified))
                        .ifPresent(dir -> {
                            deleteDirectory(dir);
                            logger.info("ディレクトリ削除: {} (jobId={})", dir.getName(), jobId);
                        });
            }
        });
    }

    /**
     * テスト環境チェック
     */
    private static boolean isTestEnvironment() {
        var profile = System.getenv(BusinessConstants.ENV_PROFILE);
        return profile == null || profile.trim().isEmpty();
    }

    /**
     * ファイルを安全に削除
     *
     * @param file 削除対象のファイル
     * @param fileType ファイルタイプ（ログ出力用）
     */
    private static void deleteFile(Path file, String fileType) {
        try {
            if (Files.deleteIfExists(file)) {
                logger.debug("{}ファイルを削除しました: {}", fileType, file.getFileName());
            }
        } catch (IOException e) {
            logger.warn("{}ファイルの削除に失敗しました {}: {}", fileType, file.getFileName(), e.getMessage());
        }
    }

    /**
     * ディレクトリを再帰的に削除
     */
    private static void deleteDirectory(File dir) {
        try (var walk = Files.walk(dir.toPath())) {
            walk.sorted(Comparator.reverseOrder())
                    .forEach(path -> path.toFile().delete());
        } catch (IOException e) {
            logger.warn("ファイルの削除に失敗しました {}", e.getMessage());
        }
    }
}