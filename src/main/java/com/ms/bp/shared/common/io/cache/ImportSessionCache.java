package com.ms.bp.shared.common.io.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * インポートセッション用簡易キャッシュ
 * 単一のインポート処理中にマスタデータの重複クエリを避けるための軽量キャッシュ
 *
 * 特徴：
 * - インポート処理期間中のみ有効
 * - スレッドセーフ
 * - LRU淘汰策略による自動メモリ管理
 * - 最大キャッシュサイズ制限
 */
public class ImportSessionCache {
    private static final Logger logger = LoggerFactory.getLogger(ImportSessionCache.class);

    // デフォルト最大キャッシュサイズ
    private static final int DEFAULT_MAX_SIZE = 20000;

    // LRUキャッシュ（アクセス順序でソート、最大サイズ制限付き）
    private final Map<String, Object> cache;

    /**
     * デフォルトコンストラクタ（最大サイズ：20000）
     */
    public ImportSessionCache() {
        this(DEFAULT_MAX_SIZE);
    }

    /**
     * 最大サイズ指定コンストラクタ
     *
     * @param maxSize 最大キャッシュサイズ
     */
    public ImportSessionCache(int maxSize) {
        // LinkedHashMapを使用してLRU機能を実装
        // accessOrder=trueでアクセス順序を有効にする
        this.cache = new LinkedHashMap<>(16, 0.75f, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry<String, Object> eldest) {
                boolean shouldRemove = size() > maxSize;
                if (shouldRemove) {
                    logger.debug("LRU除去実行: key={}, 現在サイズ={}", eldest.getKey(), size());
                }
                return shouldRemove;
            }
        };
    }

    /**
     * キャッシュからデータを取得
     *
     * @param key キャッシュキー
     * @return キャッシュされたデータ、存在しない場合はnull
     */
    @SuppressWarnings("unchecked")
    public synchronized <T> T get(String key) {
        return (T) cache.get(key);
    }

    /**
     * データをキャッシュに保存
     *
     * @param key キャッシュキー
     * @param value 保存するデータ
     */
    public synchronized void put(String key, Object value) {
        if (key != null && value != null) {
            cache.put(key, value);
            logger.debug("キャッシュ保存: key={}, valueType={}, 現在サイズ={}",
                    key, value.getClass().getSimpleName(), cache.size());
        }
    }

    /**
     * 複数のデータを一括でキャッシュに保存
     *
     * @param dataMap 保存するデータのMap
     */
    public synchronized void putAll(Map<String, Object> dataMap) {
        if (dataMap != null && !dataMap.isEmpty()) {
            cache.putAll(dataMap);
            logger.debug("キャッシュ一括保存: 件数={}, 現在サイズ={}",
                    dataMap.size(), cache.size());
        }
    }

    /**
     * キャッシュをクリア
     */
    public synchronized void clear() {
        int size = cache.size();
        cache.clear();
        logger.debug("キャッシュクリア: クリア件数={}", size);
    }

    /**
     * キャッシュサイズを取得
     *
     * @return 現在のキャッシュエントリ数
     */
    public synchronized int size() {
        return cache.size();
    }

    /**
     * キャッシュキー生成用ヘルパーメソッド
     *
     * @param masterType マスタ種別（例：AREA、GROUP、UNIT）
     * @param keyField キーフィールド名
     * @param keyValue キー値
     * @return 生成されたキャッシュキー
     */
    public static String generateKey(String masterType, String keyField, String keyValue) {
        return "%s:%s:%s".formatted(masterType, keyField, keyValue);
    }
}
