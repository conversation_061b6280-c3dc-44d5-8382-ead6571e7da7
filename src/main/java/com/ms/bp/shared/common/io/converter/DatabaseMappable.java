package com.ms.bp.shared.common.io.converter;

import com.ms.bp.shared.common.io.options.ImportOptions;

import java.util.HashMap;
import java.util.Map;

/**
 * データベースフィールドマッピング可能なオブジェクトのインターフェース
 * パフォーマンス最適化：反射を完全に回避し、直接メソッド呼び出しを可能にします
 *
 * すべてのインポート用DTOはこのインターフェースを実装する必要があります
 * テンプレートメソッドパターンを使用して固定値フィールド注入を統一処理
 * マスタデータ補完機能をサポート
 */
public interface DatabaseMappable {

    /**
     * DTOをデータベースフィールドのMapに変換します（テンプレートメソッド）
     * 固定値フィールドの注入を統一処理
     *
     * @param isInsert 挿入操作であるかどうか
     * @param options インポートオプション（固定値フィールドを含む）
     * @return データベースフィールドのMap
     */
    default Map<String, Object> toDatabaseFields(boolean isInsert, ImportOptions options) {
        Map<String, Object> fields = new HashMap<>();

        // 1. 固定値フィールドを注入（統一処理）
        if (options != null && options.getAdditionalFields() != null) {
            fields.putAll(options.getAdditionalFields());
        }

        // 2. 各DTOの核心フィールドマッピングを実行
        toDatabaseFieldsCore(isInsert,fields);

        return fields;
    }

    /**
     * DTOの核心フィールドをデータベースフィールドのMapに変換します（サブクラスで実装）
     * 各DTOは固定値フィールド以外の通常フィールドのみを処理
     *
     * @param isInsert 挿入操作であるかどうか
     * @param fields データベースフィールドのMap（固定値フィールドは含まない）
     */
    void toDatabaseFieldsCore(boolean isInsert, Map<String, Object> fields);

    /**
     * マスタデータから取得した名称情報を設定します（オプション実装）
     * 各DTOは必要に応じてこのメソッドを実装し、マスタデータから取得した名称フィールドを設定
     *
     * @param masterDataMap マスタデータのMap（キー：フィールド名、値：名称データ）
     */
    default void applyMasterData(Map<String, Object> masterDataMap) {
        // デフォルト実装は何もしない（各DTOで必要に応じてオーバーライド）
    }

    /**
     * マスタデータ補完が必要かどうかを判定します（オプション実装）
     *
     * @return マスタデータ補完が必要な場合true、不要な場合false
     */
    default boolean requiresMasterDataEnrichment() {
        return false; // デフォルトは補完不要
    }
}
