package com.ms.bp.shared.common.io.options;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 削除条件を表現するクラス
 * WHERE句の列名と値のペアを管理し、バッチ削除の最適化をサポート
 */
public class DeleteCondition {
    /**
     * -- GETTER --
     *  削除SQLを取得
     *
     */
    @Getter
    private final String deleteSql;
    private final List<Object> parameters;

    /**
     * コンストラクタ
     * @param whereColumns WHERE句の列名リスト
     * @param whereValues WHERE句の値リスト
     * @param tableName テーブル名
     */
    public DeleteCondition(List<String> whereColumns, List<Object> whereValues, String tableName) {
        if (whereColumns.size() != whereValues.size()) {
            throw new IllegalArgumentException("WHERE列数と値の数が一致しません");
        }

        // 削除SQLを構築
        if (whereColumns.isEmpty()) {
            this.deleteSql = null;
            this.parameters = new ArrayList<>();
        } else {
            String whereClause = whereColumns.stream()
                .map(col -> col + " = ?")
                .collect(Collectors.joining(" AND "));
            this.deleteSql = String.format("DELETE FROM %s WHERE %s", tableName, whereClause);
            this.parameters = new ArrayList<>(whereValues);
        }
    }

    /**
     * パラメータリストを取得
     * @return パラメータリスト
     */
    public List<Object> getParameters() {
        return new ArrayList<>(parameters);
    }

    /**
     * 削除条件が有効かどうかを判定
     * @return 有効な場合true
     */
    public boolean isValid() {
        return deleteSql != null;
    }
}
