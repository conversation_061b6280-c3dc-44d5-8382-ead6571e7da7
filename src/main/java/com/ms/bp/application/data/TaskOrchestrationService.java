package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.dto.ExportJobResult;
import com.ms.bp.application.dto.ImportJobResult;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.application.ExportJobStatusService;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.io.model.ExportResult;
import com.ms.bp.shared.common.io.model.ImportResult;
import com.ms.bp.shared.util.BackgroundTimeoutMonitor;
import com.ms.bp.shared.util.RequestContext;
import com.ms.bp.shared.util.TempFileCleanupUtil;

import java.util.List;

import com.ms.bp.shared.util.FunctionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

/**
 * タスク協調サービス
 * ビジネスフローの協調のみを担当し、技術的な実装は基盤層に委譲
 */
public class TaskOrchestrationService {
    private static final Logger logger = LoggerFactory.getLogger(TaskOrchestrationService.class);

    // 依存サービス
    private final FileExportOrchestrator fileExportOrchestrator;
    private final FileImportOrchestrator fileImportOrchestrator;
    private final ImportJobStatusService importJobStatusService;
    private final ExportJobStatusService exportJobStatusService;

    public TaskOrchestrationService() {
        this.fileExportOrchestrator = new FileExportOrchestrator();
        this.fileImportOrchestrator = new FileImportOrchestrator();
        this.importJobStatusService = new ImportJobStatusService();
        this.exportJobStatusService = new ExportJobStatusService();
    }

    /**
     * エクスポートタスクを協調
     * @param rrkBango 履歴番号
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @param context Lambda実行コンテキスト
     */
    public void orchestrateExportTask(Long rrkBango, ExportRequest exportRequest, UserInfo userInfo, Context context) {
        logger.info("エクスポートタスク協調開始: rrkBango={}", rrkBango);
        String functionId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_DOWNLOAD_CODE, exportRequest.getDataType()).getFunctionId();

        // 後台タイムアウト監視を初期化
        BackgroundTimeoutMonitor backgroundMonitor = new BackgroundTimeoutMonitor(
            context, rrkBango.toString(), jobId -> {
                try {
                    // Lambda超時による一時ファイルクリーンアップを実行
                    TempFileCleanupUtil.cleanupOldestTempDirectoriesOnTimeout(jobId);

                    // ジョブステータスをシステムエラーに更新
                    exportJobStatusService.updateJobEmergency(Long.parseLong(jobId));
                    logger.info("エクスポートジョブをシステムエラー状態に更新: rrkBango={}", jobId);

                } catch (Exception e) {
                    logger.error("エクスポートジョブ状態更新エラー: rrkBango={}", jobId, e);
                }
            });

        try {
            // 後台監視開始
            backgroundMonitor.startMonitoring();
            logger.debug("後台タイムアウト監視開始: rrkBango={}", rrkBango);

            // ビジネスフローの協調
            executeExportFlow(rrkBango, exportRequest, userInfo, context, backgroundMonitor);

            logger.info("エクスポートタスク協調正常完了: rrkBango={}", rrkBango);

        } catch (Exception e) {
            // 通常の例外処理
            logger.error(formatMessage(GlobalMessageConstants.ERR_013, functionId, e.getMessage()));
            logger.error("エクスポートフロー実行エラー: rrkBango={}", rrkBango, e);
            exportJobStatusService.updateJob(rrkBango, BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE, null);
            logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, BusinessConstants.GENERAL_ERROR_MSG));
        } finally {
            // 非同期タスクのコンテキストをクリア（メモリリーク防止）
            RequestContext.clear();

            // 後台監視のクリーンアップ（重要：リソースリーク防止）
            try {
                backgroundMonitor.cleanup();
                logger.debug("後台監視クリーンアップ完了: rrkBango={}", rrkBango);
            } catch (Exception cleanupException) {
                logger.error("後台監視クリーンアップエラー: rrkBango={}", rrkBango, cleanupException);
            }

            logger.debug("非同期タスクのコンテキストをクリアしました: rrkBango={}", rrkBango);
        }
    }

    /**
     * インポートタスクを協調
     * @param jobId ジョブID
     * @param importRequest インポートリクエスト
     * @param userInfo ユーザー情報
     * @param context Lambda実行コンテキスト
     */
    public void orchestrateImportTask(Long jobId, ImportRequest importRequest, UserInfo userInfo, Context context) {
        logger.info("インポートタスク協調開始: jobId={}", jobId);
        String functionId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE, importRequest.getDataType()).getFunctionId();

        // 後台タイムアウト監視を初期化
        BackgroundTimeoutMonitor backgroundMonitor = new BackgroundTimeoutMonitor(
            context, jobId.toString(), jId -> {
                try {
                    importJobStatusService.updateJobEmergency(Long.parseLong(jId));
                    logger.info("インポートジョブをシステムエラー状態に更新: jobId={}", jId);
                } catch (Exception e) {
                    logger.error("インポートジョブ状態更新エラー: jobId={}", jId, e);
                }
            });

        try {
            // 後台監視開始
            backgroundMonitor.startMonitoring();
            logger.debug("後台タイムアウト監視開始: jobId={}", jobId);

            // ユーザー情報を復元
            RequestContext.setUserInfo(userInfo);
            logger.debug("非同期タスクでユーザー情報を復元しました: jobId={}", jobId);

            // ビジネスフローの協調
            executeImportFlow(jobId, importRequest, context, functionId, backgroundMonitor);

            logger.info("インポートタスク協調正常完了: jobId={}", jobId);

        } catch (Exception e) {
            // ERR_010（ファイル形式エラー）の特殊処理
            if (e instanceof ServiceException serviceException
                    && GlobalMessageConstants.ERR_010.getCode().equals(serviceException.getCode())) {
                // ERR_010特殊処理：ファイル形式エラー
                logger.error(formatMessage(GlobalMessageConstants.ERR_010, importRequest.getDataType()));
                logger.error("ファイル形式エラー: jobId={}", jobId, e);
                importJobStatusService.updateJob(jobId, BusinessConstants.BATCH_STATUS_PARTIALLY_FAILED_CODE, ImportJobResult.failed(null));
                logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, BusinessConstants.FILE_FORMAT_ERROR_MSG));
                return;
            }

            // その他の例外の既存処理
            logger.error(formatMessage(GlobalMessageConstants.ERR_013, functionId, e.getMessage()));
            logger.error("インポートフロー実行エラー: jobId={}", jobId, e);
            importJobStatusService.updateJob(jobId, BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE, null);
            logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, BusinessConstants.GENERAL_ERROR_MSG));
        } finally {
            // 非同期タスクのコンテキストをクリア
            RequestContext.clear();
            // 後台監視のクリーンアップ
            try {
                backgroundMonitor.cleanup();
                logger.debug("後台監視クリーンアップ完了: jobId={}", jobId);
            } catch (Exception cleanupException) {
                logger.error("後台監視クリーンアップエラー: jobId={}", jobId, cleanupException);
            }
            logger.debug("非同期タスクのコンテキストをクリアしました: jobId={}", jobId);
        }
    }

    // ==================== プライベートメソッド ====================

    /**
     * エクスポートフローを実行
     */
    private void executeExportFlow(Long rrkBango, ExportRequest exportRequest, UserInfo userInfo,
                                 Context context, BackgroundTimeoutMonitor backgroundMonitor) {
        FileExportOrchestrator.ExportProcessResult result = null;
        try {
            // ステップ1: ファイルエクスポート処理
            result = fileExportOrchestrator.processExport(
                rrkBango.toString(), exportRequest, userInfo, context);

            // 後台監視でタイムアウトが検出された場合は処理を中断
            if (backgroundMonitor.isTimeoutDetected()) {
                logger.warn("ファイルエクスポート処理中にタイムアウト検出: rrkBango={}", rrkBango);
                return;
            }

            // ステップ2: S3アップロード協調（データタイプに応じた専用ディレクトリ使用）
            String s3Key = fileExportOrchestrator.coordinateS3Upload(result, rrkBango.toString(), exportRequest.getDataType());

            // 後台監視でタイムアウトが検出された場合は処理を中断
            if (backgroundMonitor.isTimeoutDetected()) {
                logger.warn("S3アップロード処理中にタイムアウト検出: rrkBango={}", rrkBango);
                return;
            }

            // ステップ4: ExportResult統計に基づいてステータスを決定
            String finalStatus = determineJobStatus(result.exportResults());
            ExportJobResult jobResult = createExportJobResult(result, s3Key);
            exportJobStatusService.updateJob(rrkBango, finalStatus, jobResult);

            logger.info("エクスポートフロー完了: rrkBango={}, finalStatus={}", rrkBango, finalStatus);

        } finally {
            // 異常終了やタイムアウトの場合で、まだクリーンアップが完了していない場合のみ実行
            if (result != null) {
                TempFileCleanupUtil.cleanupExportTempFiles(result.csvFiles(), result.zipFile());
            }
        }
    }

    /**
     * インポートフローを実行
     */
    private void executeImportFlow(Long jobId, ImportRequest importRequest, Context context,
                                 String functionId, BackgroundTimeoutMonitor backgroundMonitor) {
        // ステップ1: ファイルインポート処理
        ImportResult result = fileImportOrchestrator.processImport(jobId, importRequest, context);

        // 後台監視でタイムアウトが検出された場合は処理を中断
        if (backgroundMonitor.isTimeoutDetected()) {
            logger.warn("ファイルインポート処理中にタイムアウト検出: jobId={}", jobId);
            return;
        }

        // ステップ2: 完了ステータス更新
        String errorFileS3Key;
        if (result.getStatistics().containsKey("errorFileS3Key")) {
            logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, "File Check Error"));
            errorFileS3Key = (String) result.getStatistics().get("errorFileS3Key");
            importJobStatusService.updateJob(jobId, BusinessConstants.BATCH_STATUS_FAILED_CODE, ImportJobResult.completed(errorFileS3Key));
        } else {
            logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, "Success"));
            importJobStatusService.updateJob(jobId, BusinessConstants.BATCH_STATUS_COMPLETED_CODE, null);
        }
        logger.debug("インポートフロー完了: jobId={}", jobId);
    }

    /**
     * ExportResult統計に基づいてジョブステータスを決定
     * @param exportResults エクスポート結果リスト
     * @return ステータスコード (完了/一部失敗/失敗)
     */
    private String determineJobStatus(List<ExportResult> exportResults) {
        if (exportResults == null || exportResults.isEmpty()) {
            logger.warn("エクスポート結果が空です。失敗として処理します。");
            return BusinessConstants.BATCH_STATUS_FAILED_CODE; // 失敗
        }

        int totalTasks = exportResults.size();
        int failedTasks = 0;

        for (ExportResult result : exportResults) {
            Boolean completed = (Boolean) result.getStatistics().get("completed");
            if (completed == null || !completed) {
                failedTasks++;
            }
        }

        logger.info("エクスポートタスク統計: 総タスク数={}, 失敗タスク数={}", totalTasks, failedTasks);

        if (failedTasks == 0) {
            return BusinessConstants.BATCH_STATUS_COMPLETED_CODE; // 完了
        } else if (failedTasks == totalTasks) {
            return BusinessConstants.BATCH_STATUS_FAILED_CODE; // 失敗
        } else {
            return BusinessConstants.BATCH_STATUS_PARTIALLY_FAILED_CODE; // 一部失敗
        }
    }

    /**
     * エクスポートジョブ結果を作成
     */
    private ExportJobResult createExportJobResult(FileExportOrchestrator.ExportProcessResult result, String s3Key) {
        try {
            long fileSize = result.zipFile() != null ? java.nio.file.Files.size(result.zipFile()) : 0;
            String fileName = result.zipFile() != null ? result.zipFile().getFileName().toString() : "export.zip";

            return ExportJobResult.completed(
                    s3Key, // s3Key
                    fileName,
                    fileSize
            );
        } catch (Exception e) {
            logger.error("エクスポートジョブ結果作成エラー", e);
            return ExportJobResult.completed(s3Key, "export.zip", 0L);
        }
    }

}
