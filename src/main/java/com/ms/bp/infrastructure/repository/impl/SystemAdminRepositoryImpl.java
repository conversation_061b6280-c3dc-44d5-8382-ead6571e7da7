package com.ms.bp.infrastructure.repository.impl;


import com.ms.bp.domain.master.repository.SystemAdminRepository;
import com.ms.bp.infrastructure.repository.dao.ConcurrentJobDataAccess;
import com.ms.bp.infrastructure.repository.dao.SystemAdminDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Optional;

/**
 * システム管理者マスタリポジトリ実装クラス
 */
public class SystemAdminRepositoryImpl implements SystemAdminRepository {
    private static final Logger logger = LoggerFactory.getLogger(SystemAdminRepositoryImpl.class);

    private final SystemAdminDataAccess dataAccess;

    public SystemAdminRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new SystemAdminDataAccess(jdbcTemplate);
    }

    @Override
    public boolean isValidSystemAdmin(String shainCode, String systemOperationCompanyCode) {
        try {
            logger.debug("システム管理者確認開始: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode);
            
            var isAdmin = dataAccess.isValidSystemAdmin(shainCode, systemOperationCompanyCode);
            
            logger.debug("システム管理者確認完了: shainCode={}, 結果={}", 
                        shainCode, isAdmin ? "管理者" : "一般ユーザー");
            
            return isAdmin;
            
        } catch (SQLException e) {
            logger.error("システム管理者確認中にエラーが発生しました: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode, e);
            // エラーの場合は管理者ではないとして扱う
            return false;
        }
    }
}