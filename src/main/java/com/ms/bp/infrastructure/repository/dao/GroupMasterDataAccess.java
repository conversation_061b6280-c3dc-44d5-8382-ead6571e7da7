package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * グループマスタデータアクセス実装
 * M_GROUPMST（グループマスタ）への具体的なデータアクセスを実装
 */
public class GroupMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(GroupMasterDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_GROUP_CODE_SQL = """
        SELECT 1
        FROM M_GROUPMST
        WHERE GROUP_CODE = ?
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
        AND SHIYO_KNSH_KUBUN = '0'
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public GroupMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * グループコードでグループマスタの存在チェック
     *
     * @param groupCode グループコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByGroupCode(String groupCode) throws SQLException {
        logger.debug("グループマスタ存在チェック開始: グループコード={}", groupCode);

        List<Integer> results = jdbcTemplate.query(
            EXISTS_BY_GROUP_CODE_SQL,
            new Object[]{groupCode},
            rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("グループマスタ存在チェック完了: グループコード={}, 存在={}", groupCode, exists);

        return exists;
    }
}
