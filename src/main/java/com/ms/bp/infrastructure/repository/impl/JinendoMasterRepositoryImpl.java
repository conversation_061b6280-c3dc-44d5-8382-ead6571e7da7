package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.repository.JinendoMasterRepository;
import com.ms.bp.infrastructure.repository.dao.JinendoMasterDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;

/**
 * 次年度計画マスタリポジトリ実装
 * T_JINENDO_KKK（次年度計画マスタ）へのデータアクセスを実装
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public class JinendoMasterRepositoryImpl implements JinendoMasterRepository {
    private static final Logger logger = LoggerFactory.getLogger(JinendoMasterRepositoryImpl.class);

    private final JinendoMasterDataAccess dataAccess;

    public JinendoMasterRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new JinendoMasterDataAccess(jdbcTemplate);
    }

    @Override
    public boolean existsBySaisanCode(String nendo, String saisanCode, String areaCode, String groupCode, String unitCode) {
        try {
            boolean exists = dataAccess.existsBySaisanCode(nendo, saisanCode, areaCode, groupCode, unitCode);
            if(!exists){
                exists = dataAccess.existsByIknskSaisanCode(nendo, saisanCode, areaCode, groupCode, unitCode);
            }
            logger.debug("次年度計画マスタ存在チェックを実行しました: 採算管理単位コード={}, 存在={}", saisanCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("次年度計画マスタ存在チェック中にエラーが発生しました: 採算管理単位コード={}", saisanCode, e);
            throw new RuntimeException("次年度計画マスタ存在チェックに失敗しました", e);
        }
    }

    /*
    @Override
    public boolean existsBySaisanCode(String nendo, String saisanCode, String areaCode, String groupCode) {
        try {
            boolean exists = dataAccess.existsBySaisanCode(nendo, saisanCode, areaCode, groupCode);
            logger.debug("次年度計画マスタ存在チェックを実行しました: 採算管理単位コード={}, 存在={}", saisanCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("次年度計画マスタ存在チェック中にエラーが発生しました: 採算管理単位コード={}", saisanCode, e);
            throw new RuntimeException("次年度計画マスタ存在チェックに失敗しました", e);
        }
    }
     */
}
