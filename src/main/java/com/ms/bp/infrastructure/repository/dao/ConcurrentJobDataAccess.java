package com.ms.bp.infrastructure.repository.dao;


import com.ms.bp.domain.master.model.ConcurrentJobInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * 兼務マスタデータアクセス実装
 */
public class ConcurrentJobDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(ConcurrentJobDataAccess.class);

    private final JdbcTemplate jdbcTemplate;

    public ConcurrentJobDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 指定社員の有効な兼務情報をエリアコード・グループコード付きで取得
     * 兼務マスタ、ユニットマスタ、グループマスタを連携して一括取得
     * 使用禁止区分が'0'の有効なレコードのみを対象とする
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 有効な兼務情報リスト（エリアコード・グループコード付き）
     * @throws SQLException データベースアクセスエラー
     */
    public List<ConcurrentJobInfo> findConcurrentJobsWithAreaInfo(String shainCode, String systemOperationCompanyCode) throws SQLException {
        // 有効な兼務情報（エリア・グループ付き）を取得
        var sql = """
            SELECT DISTINCT
                k.SYSTM_UNYO_KIGYO_CODE,
                k.SHAIN_CODE,
                k.UNIT_CODE,
                k.YKSHK_KUBUN,
                g.GROUP_CODE,
                g.AREA_CODE
            FROM M_JK_KENMUMASTER k
            INNER JOIN M_UNITMST u ON k.UNIT_CODE = u.UNIT_CODE
                AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= u.KSHB
                AND u.SHRYB >= TO_CHAR(CURRENT_DATE, 'YYYYMMDD')
                AND u.SHIYO_KNSH_KUBUN = '0'
            INNER JOIN M_GROUPMST g ON u.GROUP_CODE = g.GROUP_CODE
                AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= g.KSHB
                AND g.SHRYB >= TO_CHAR(CURRENT_DATE, 'YYYYMMDD')
                AND g.SHIYO_KNSH_KUBUN = '0'
            WHERE k.SHAIN_CODE = ?
            AND k.SYSTM_UNYO_KIGYO_CODE = ?
            AND k.YKSHK_KUBUN IN ('41','51','61')
            ORDER BY k.UNIT_CODE
            """;

        logger.debug("兼務情報（エリア・グループ付き）取得SQL実行: shainCode={}, systemOperationCompanyCode={}",
                    shainCode, systemOperationCompanyCode);

        var params = new Object[]{shainCode, systemOperationCompanyCode};

        return jdbcTemplate.query(sql, params, (rs) -> {
            var concurrentJob = new ConcurrentJobInfo();
            concurrentJob.setSystemOperationCompanyCode(rs.getString("SYSTM_UNYO_KIGYO_CODE"));
            concurrentJob.setShainCode(rs.getString("SHAIN_CODE"));
            concurrentJob.setUnitCode(rs.getString("UNIT_CODE"));
            concurrentJob.setPositionDivision(rs.getString("YKSHK_KUBUN"));
            concurrentJob.setAreaCode(rs.getString("AREA_CODE"));
            concurrentJob.setGroupCode(rs.getString("GROUP_CODE"));
            return concurrentJob;
        });
    }
}