package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.model.UnitGroupInfo;
import com.ms.bp.domain.master.repository.UnitMasterRepository;
import com.ms.bp.infrastructure.repository.dao.UnitMasterDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Optional;

/**
 * ユーザーマスタリポジトリ実装
 * M_UNITMST（ユニットマスタ）へのデータアクセスを実装
 */
public class UnitMasterRepositoryImpl implements UnitMasterRepository {
    private static final Logger logger = LoggerFactory.getLogger(UnitMasterRepositoryImpl.class);

    private final UnitMasterDataAccess dataAccess;

    public UnitMasterRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new UnitMasterDataAccess(jdbcTemplate);
    }

    @Override
    public boolean existsByUnitCode(String unitCode) {
        try {
            boolean exists = dataAccess.existsByUnitCode(unitCode);
            logger.debug("ユニットマスタ存在チェックを実行しました: ユニットコード={}, 存在={}", unitCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("ユニットマスタ存在チェック中にエラーが発生しました: ユニットコード={}", unitCode, e);
            throw new RuntimeException("ユニットマスタ存在チェックに失敗しました", e);
        }
    }

    @Override
    public Optional<UnitGroupInfo> findUnitGroupInfoByUnitCode(String unitCode) {
        try {
            Optional<UnitGroupInfo> result = dataAccess.findUnitGroupInfoByUnitCode(unitCode);
            if (result.isPresent()) {
                logger.debug("ユニット・グループ統合情報を取得しました: ユニットコード={}, ユニット名={}, グループ名={}",
                        unitCode, result.get().getUnitName(), result.get().getGroupName());
            } else {
                logger.debug("ユニット・グループ統合情報が見つかりませんでした: ユニットコード={}", unitCode);
            }
            return result;
        } catch (SQLException e) {
            logger.error("ユニット・グループ統合情報取得中にエラーが発生しました: ユニットコード={}", unitCode, e);
            throw new RuntimeException("ユニット・グループ統合情報取得に失敗しました", e);
        }
    }
}
