package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.repository.AreaCodeRepository;
import com.ms.bp.infrastructure.repository.dao.AreaCodeDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * エリアコードリポジトリ実装
 * M_SK_KKK_AREA（採算管理単位計画策定エリアマスタ）へのデータアクセスを実装
 */
public class AreaCodeRepositoryImpl implements AreaCodeRepository {
    private static final Logger logger = LoggerFactory.getLogger(AreaCodeRepositoryImpl.class);

    private final AreaCodeDataAccess dataAccess;

    /**
     * コンストラクタ構築
     *
     * @param jdbcTemplate DBツール
     */
    public AreaCodeRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new AreaCodeDataAccess(jdbcTemplate);
    }

    /**
     * 年度とシステム運用企業コードでエリアコードリストを取得
     *
     * @param nendo 年度（YYYY形式）
     * @param systemOperationCompanyCode システム運用企業コード
     * @return エリアコードリスト（エリア表示順でソート済み）
     * @throws SQLException データベースアクセスエラー
     */
    @Override
    public List<String> findAreaCodesByNendo(String nendo, String systemOperationCompanyCode) {
        try {
            List<String> areaCodes = dataAccess.findAreaCodesByNendo(nendo, systemOperationCompanyCode);
            logger.debug("エリアコードを取得しました: 年度={}, 企業コード={}, 件数={}",
                        nendo, systemOperationCompanyCode, areaCodes.size());
            return areaCodes;
        } catch (SQLException e) {
            logger.error("エリアコード取得中にエラーが発生しました: 年度={}, 企業コード={}",
                        nendo, systemOperationCompanyCode, e);
            throw new RuntimeException("エリアコード取得に失敗しました", e);
        }
    }

    /**
     * 見通しエクスポート_年度とシステム運用企業コードちエリアコードでエリア情報リストを取得
     * エリアコードが重複した場合は、重複削除要
     * SKSAが含まれている場合、データベースから実際のエリアコードを取得して置換
     *
     * @param nendo 年度（YYYY形式）
     * @param systemOperationCompanyCode システム運用企業コード(SKSA)
     * @param areaCodes エリアコードリスト
     * @return 処理済みエリアコードリスト
     * @throws SQLException データベースアクセスエラー
     */
    @Override
    public List<AreaInfo> findAreaInfosForMsth(String nendo, String systemOperationCompanyCode, List<String> areaCodes) {
        try {
            List<AreaInfo> areaInfos = dataAccess.findAreaInfosForMsth(nendo, systemOperationCompanyCode,areaCodes);
            logger.debug("エリア情報を取得しました: 年度={}, 企業コード={}, 件数={}",
                    nendo, systemOperationCompanyCode, areaInfos.size());
            return areaInfos;
        } catch (SQLException e) {
            logger.error("エリア情報取得中にエラーが発生しました: 年度={}, 企業コード={}",
                    nendo, systemOperationCompanyCode, e);
            throw new RuntimeException("エリア情報取得に失敗しました", e);
        }
    }

    /**
     * エリアコードでエリア名称を取得
     * M_SOSHIKIAREAMST（組織エリアマスタ）からエリア名短縮漢字を取得する
     *
     * @param areaCode エリアコード（4桁）
     * @return エリア名称（該当データがない場合は空のOptional）
     */
    @Override
    public Optional<String> findAreaNameByAreaCode(String areaCode) {
        try {
            logger.debug("エリア名称取得開始: エリアコード={}", areaCode);

            Optional<String> areaName = dataAccess.findAreaNameByAreaCode(areaCode);

            if (areaName.isPresent()) {
                logger.debug("エリア名称を取得しました: エリアコード={}, エリア名称={}",
                           areaCode, areaName.get());
            } else {
                logger.debug("エリア名称が見つかりませんでした: エリアコード={}", areaCode);
            }

            return areaName;

        } catch (SQLException e) {
            logger.error("エリア名称取得中にエラーが発生しました: エリアコード={}", areaCode, e);
            throw new RuntimeException("エリア名称取得に失敗しました", e);
        }
    }
}
