package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.permission.model.Permission;
import com.ms.bp.domain.permission.model.PersonalPermission;
import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.infrastructure.repository.dao.PermissionDataAccess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * 権限リポジトリ実装クラス
 * 領域インターフェースと基盤設備層DAOの橋渡しを行う
 */
public class PermissionRepositoryImpl implements PermissionRepository {
    private static final Logger logger = LoggerFactory.getLogger(PermissionRepositoryImpl.class);
    
    private final PermissionDataAccess dataAccess;

    public PermissionRepositoryImpl(PermissionDataAccess dataAccess) {
        this.dataAccess = dataAccess;
    }
    

    // ==================== 権限ルールマスタ関連 ====================

    @Override
    public List<PersonalPermission> findPersonalPermissions(String shainCode,
                                                            String systemOperationCompanyCode) {
        try {
            return dataAccess.findPersonalPermissions(shainCode, systemOperationCompanyCode);
        } catch (SQLException e) {
            logger.error("個人権限検索中にエラーが発生しました: shainCode={}",
                    shainCode,  e);
            throw new RuntimeException("個人権限検索に失敗しました", e);
        }
    }


    @Override
    public List<Permission> findCommonPermissions(String systemOperationCompanyCode,
                                                          String userUnitCode,
                                                          String areaCode) {
        try {
            logger.debug("共通権限情報取得開始: systemOperationCompanyCode={}, userUnitCode={}, areaCode={}",
                        systemOperationCompanyCode,userUnitCode,areaCode);

            // 一条SQLで権限情報を取得
            List<Permission> permissions = dataAccess.findCommonPermissions(
                systemOperationCompanyCode,
                userUnitCode,
                areaCode
            );

            logger.debug("共通権限情報取得結果: {}件の権限が見つかりました", permissions.size());
            return permissions;

        } catch (SQLException e) {
            logger.error("共通権限情報取得中にエラーが発生しました", e);
            throw new RuntimeException("共通権限情報取得に失敗しました", e);
        }
    }

    // ==================== 権限ルールマスタ関連 ====================
    @Override
    public List<String> findRuleTypeCodesByMsStrategyCorp(String hantCode, String systemOperationCompanyCode) {
        try {
            logger.debug("権限ルール種類コード取得開始: hantCode={}, systemOperationCompanyCode={}",
                        hantCode, systemOperationCompanyCode);

            List<String> typeCodes = dataAccess.findRuleTypeCodesByMsStrategyCorp(hantCode, systemOperationCompanyCode);

            logger.debug("権限ルール種類コード取得結果: {}件の種類コードが見つかりました", typeCodes.size());
            return typeCodes;

        } catch (SQLException e) {
            logger.error("権限ルール種類コード取得中にエラーが発生しました: hantCode={}, systemOperationCompanyCode={}",
                        hantCode, systemOperationCompanyCode, e);
            throw new RuntimeException("権限ルール種類コード取得に失敗しました", e);
        }
    }

    @Override
    public List<AreaInfo> findAreaInfosByAreaTantoshaPermission(String systemOperationCompanyCode) {
        try {
            logger.debug("エリア担当者権限エリア情報取得開始: systemOperationCompanyCode={}",
                        systemOperationCompanyCode);

            List<AreaInfo> areaInfos = dataAccess.findAreaInfosByAreaTantoshaPermission(systemOperationCompanyCode);

            logger.debug("エリア担当者権限エリア情報取得結果: {}件のエリア情報が見つかりました", areaInfos.size());
            return areaInfos;

        } catch (SQLException e) {
            logger.error("エリア担当者権限エリア情報取得中にエラーが発生しました: systemOperationCompanyCode={}",
                        systemOperationCompanyCode, e);
            throw new RuntimeException("エリア担当者権限エリア情報取得に失敗しました", e);
        }
    }

    // ==================== システム管理者権限関連 ====================

    @Override
    public List<Permission> findAllValidHeadOfficePermissions(String systemOperationCompanyCode) {
        try {
            logger.debug("システム管理者本社権限取得開始: systemOperationCompanyCode={}",
                        systemOperationCompanyCode);

            List<Permission> permissions = dataAccess.findAllValidHeadOfficePermissions(systemOperationCompanyCode);

            logger.debug("システム管理者本社権限取得結果: {}件の権限が見つかりました", permissions.size());
            return permissions;

        } catch (SQLException e) {
            logger.error("システム管理者本社権限取得中にエラーが発生しました: systemOperationCompanyCode={}",
                        systemOperationCompanyCode, e);
            throw new RuntimeException("システム管理者本社権限取得に失敗しました", e);
        }
    }

}
