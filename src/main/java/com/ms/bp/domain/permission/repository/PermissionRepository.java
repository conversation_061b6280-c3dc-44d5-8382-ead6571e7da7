package com.ms.bp.domain.permission.repository;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.permission.model.Permission;
import com.ms.bp.domain.permission.model.PermissionRule;
import com.ms.bp.domain.permission.model.PersonalPermission;

import java.util.List;
import java.util.Optional;

/**
 * 権限リポジトリ領域インターフェース
 * 権限領域における権限データアクセスの抽象化
 * データ永続化の具体実装から領域層を分離する
 */
public interface PermissionRepository {
    

    // ==================== 個人権限設定マスタ関連 ====================

    /**
     * 指定条件で個人権限を検索
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 個人権限リスト
     */
    List<PersonalPermission> findPersonalPermissions(String shainCode,
                                                    String systemOperationCompanyCode);
    

    // ==================== 権限チェック用の複合メソッド ====================
    /**
     * ユーザーの権限情報を取得（共通ルール・最適化版）
     * M_KENとM_KEN_RULEを連表して一条SQLで効率的に権限情報を取得
     * 権限チェックと同時に権限の詳細情報も取得可能
     * 日付比較にはCURRENT_DATEを使用し、DATA_KUBUN_PTTRN字段は除外
     *
     * @param systemOperationCompanyCode システム運用企業コード
     * @param userUnitCode ユーザーのユニットコード
     * @param areaCode エリアコード
     * @return 権限情報のリスト（権限がない場合は空のリスト）
     */
    List<Permission> findCommonPermissions(String systemOperationCompanyCode,
                                                   String userUnitCode,
                                                   String areaCode);

    // ==================== 権限ルールマスタ関連 ====================
    /**
     * 指定された判定コードとシステム運用企業コードに対応する種類コードリストを取得
     * M_KEN_RULEテーブルから条件に一致する種類コードを検索
     *
     * @param hantCode 判定コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 種類コードのリスト（該当データがない場合は空のリスト）
     */
    List<String> findRuleTypeCodesByMsStrategyCorp(String hantCode, String systemOperationCompanyCode);

    /**
     * エリア担当者権限に基づくエリア情報リストを取得
     * 権限ルールマスタから判定コード（エリア担当者）の種類コードを取得し、
     * 組織エリアマスタと関連付けてエリア情報を取得する
     *
     * ビジネスルール：
     * - 判定コード（エリア担当者）の権限ルールを対象とする
     * - 有効期間内（CURRENT_DATE）のレコードのみ取得
     * - 使用禁止でないエリア情報のみ取得
     * - エリアコード順でソート
     *
     * @param systemOperationCompanyCode システム運用企業コード
     * @return エリア情報リスト（エリアコードとエリア名称の組み合わせ）
     */
    List<AreaInfo> findAreaInfosByAreaTantoshaPermission(String systemOperationCompanyCode);

    // ==================== システム管理者権限関連 ====================

    /**
     * システム管理者用の全有効本社権限を取得
     * 権限マスタから有効期間内の全権限を取得し、本社権限（権限コード3桁目='H'）のみを抽出
     *
     * ビジネスルール：
     * - 有効期間内（CURRENT_DATE）の権限のみ取得
     * - 使用禁止でない権限のみ取得
     * - 権限コード3桁目が'H'の本社権限のみ抽出
     * - 権限コード順でソート
     *
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 本社権限リスト（該当データがない場合は空のリスト）
     */
    List<Permission> findAllValidHeadOfficePermissions(String systemOperationCompanyCode);
}
