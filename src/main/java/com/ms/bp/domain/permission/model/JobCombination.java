package com.ms.bp.domain.permission.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 職務組み合わせ値オブジェクト
 * 権限判定に必要な職務関連情報を保持
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JobCombination {
    
    /**
     * ユニットコード（5桁）
     * 社員マスタまたは兼務マスタから取得
     */
    private String unitCode;
    
    /**
     * 役職区分コード（2桁）
     * 社員マスタまたは兼務マスタから取得
     */
    private String positionCode;
    
    /**
     * エリアコード（4桁）
     * グループマスタから取得
     */
    private String areaCode;
    
    /**
     * グループコード
     * ユニットマスタから取得
     */
    private String groupCode;


}
