package com.ms.bp.domain.file.indirectprofitmaker.strategy;

import com.ms.bp.application.data.strategy.DataAccessStrategy;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 間接利益計画_メーカー別データ取得SQL戦略
 */
public class IndirectProfitMakerDataStrategy implements DataAccessStrategy {
    private static final Logger logger = LoggerFactory.getLogger(IndirectProfitMakerDataStrategy.class);

    @Override
    public SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("間接利益計画_メーカー別SQL構築開始: ユーザー={}", userInfo.getShainCode());

        // 年度パラメータの設定
        String nendoParam = DateUtil.getNextFiscalYear();

        // パラメータの取得と処理
        String hnshBashoKubun = exportRequest.getHnshBashoKubun();
        String ctgryKubun = exportRequest.getCtgryKubun();
        List<String> areaCodeList = exportRequest.getArea();

        // SQLクエリとパラメータリストを構築
        SqlWithParams queryResult = buildIndirectProfitMakerQueryWithParams(
                nendoParam, hnshBashoKubun, ctgryKubun, areaCodeList);

        logger.info("間接利益計画_メーカー別SQL構築完了: 年度={}, エリア数={}", nendoParam, areaCodeList.size());
        return queryResult;
    }

    @Override
    public String getStrategyName() {
        return BusinessConstants.INDIRECT_PROFIT_MAKER_DEFAULT;
    }

    /**
     * 間接利益計画_メーカー別クエリとパラメータを構築
     *
     * @param nendoParam 年度パラメータ
     * @param hnshBashoKubun 本社・場所区分
     * @param ctgryKubun カテゴリー区分
     * @param areaCodeList エリアコードリスト
     * @return SQLクエリとパラメータ
     */
    private SqlWithParams buildIndirectProfitMakerQueryWithParams(String nendoParam, 
            String hnshBashoKubun, String ctgryKubun, List<String> areaCodeList) {

        // パラメータリストを作成（順序重要）
        List<Object> paramList = new ArrayList<>();

        // 1. 年度パラメータを追加
        paramList.add(nendoParam);

        // 2. 本社・場所区分の処理（"2"の場合は"0"と"1"の両方、それ以外はそのまま）
        List<String> hnshBashoList = BusinessConstants.HONSHA_BASHO_ALL_CODE.equals(hnshBashoKubun)
                ? Arrays.asList(BusinessConstants.HONSHA_KUBUN_CODE, BusinessConstants.BASHO_KUBUN_CODE)
                : Collections.singletonList(hnshBashoKubun);
        String hnshBashoCondition = buildInCondition(hnshBashoList.size());
        paramList.addAll(hnshBashoList);

        // 3. カテゴリーコードの処理（"5"の場合は"1"～"4"の全て、それ以外はそのまま）
        List<String> ctgryCodeList = BusinessConstants.CATEGORY_ALL_CODE.equals(ctgryKubun)
                ? Arrays.asList("1", "2", "3", "4")
                : Collections.singletonList(ctgryKubun);
        String ctgryCondition = buildInCondition(ctgryCodeList.size());
        paramList.addAll(ctgryCodeList);

        // 4. エリアコード条件の構築
        String areaCondition = buildInCondition(areaCodeList.size());
        paramList.addAll(areaCodeList);

        // SQLクエリを構築
        String sql = buildIndirectProfitMakerQueryWithPositionalParams(
                hnshBashoCondition, ctgryCondition, areaCondition);

        // 順序保持のためLinkedHashMapを使用してパラメータマップを構築
        Map<String, Object> paramMap = new LinkedHashMap<>();
        for (int i = 0; i < paramList.size(); i++) {
            paramMap.put("param" + i, paramList.get(i));
        }

        logger.debug("構築されたパラメータ順序: {}", paramList);

        return new SqlWithParams(sql, paramMap);
    }

    /**
     * IN条件のプレースホルダーを構築
     *
     * @param count パラメータ数
     * @return IN条件文字列
     */
    private String buildInCondition(int count) {
        return count == 1 ? "= ?" :
                "IN (" + String.join(",", Collections.nCopies(count, "?")) + ")";
    }

    /**
     * 間接利益計画_メーカー別用SQLクエリを構築
     *
     * @param hnshBashoCondition 本社・場所区分条件
     * @param ctgryCondition カテゴリー条件
     * @param areaCondition エリア条件
     * @return SQLクエリ
     */
    private String buildIndirectProfitMakerQueryWithPositionalParams(
            String hnshBashoCondition, String ctgryCondition, String areaCondition) {
        
        return String.format("""
            SELECT 
                -- 年度
                km.NENDO AS fiscal_year,
            
                -- 出力対象（固定値）
                '間接' AS output_target,
                
                -- カテゴリーコード
                km.CTGRY_CODE AS ctgry_code,
                
                -- カテゴリ名
                CASE km.CTGRY_CODE
                    WHEN '1' THEN '加食'
                    WHEN '2' THEN '低温'
                    WHEN '3' THEN '酒類'
                    WHEN '4' THEN '菓子'
                END AS ctgry_mei,
                
                -- 本社・場所区分
                km.HNSH_BASHO_KUBUN AS hnsh_basho_kubun,
                
                -- 本社・場所
                CASE km.HNSH_BASHO_KUBUN
                    WHEN '0' THEN '本社'
                    WHEN '1' THEN '場所'
                END AS hnsh_basho,
                
                -- メーカーコード
                km.MAKER_CODE AS maker_code,
                
                -- メーカー名
                mm.MAKER_MEI_KANJI AS maker_mei,
                
                -- メーカー別管理No.
                km.MAKER__KANRI_NO AS maker_kanri_no,
                
                -- 採算管理単位コード
                km.SSNKN_TNCD AS ssnkn_tncd,
                
                -- 採算管理単位名
                COALESCE(jn.SSN_KANRI_TNM_KANJI, sm.SSN_KANRI_TNM_KANJI) AS ssn_kanri_tnm_kanji,
                
                -- 会社コード
                gm.SYSTM_UNYO_KIGYO_CODE AS kigyo_code,
                
                -- 会社名
                sys.SYSTM_UNYO_KGYM_KANJI AS kigyo_mei,
                
                -- エリアコード
                gm.AREA_CODE AS area_code,
                
                -- エリア名
                sa.AREA_MEI_KANJI AS area_mei,
                
                -- サブエリアコード
                gm.SUB_AREA_CODE AS sub_area_code,
                
                -- サブエリア名
                sa.SUB_AREA_MEI_KANJI AS sub_area_mei,
                
                -- グループコード
                km.GROUP_CODE AS group_code,
                
                -- グループ名
                gm.GROUP_MEI_KANJI AS group_mei,
                
                -- 未収区分
                km.MISHU_KUBUN AS mishu_kubun,
                
                -- 未収名
                CASE km.MISHU_KUBUN
                    WHEN '1' THEN '企画'
                    WHEN '2' THEN '特別'
                    WHEN '3' THEN '年契'
                    WHEN '4' THEN 'その他'
                END AS mishu_mei,
                
                -- 在直区分
                km.ZAICHOKU_KUBUN AS zaichoku_kubun,
                
                -- 在庫直送
                CASE km.ZAICHOKU_KUBUN
                    WHEN '1' THEN '在庫'
                    WHEN '3' THEN '直送'
                END AS zaichoku_mei,
                
                -- 月別間接利益（千円単位に変換）
                ROUND(km.KNSTS_RIEKI_KKK_1_TSKM / 1000) AS april_profit,
                ROUND(km.KNSTS_RIEKI_KKK_2_TSKM / 1000) AS may_profit,
                ROUND(km.KNSTS_RIEKI_KKK_3_TSKM / 1000) AS june_profit,
                ROUND(km.KNSTS_RIEKI_KKK_4_TSKM / 1000) AS july_profit,
                ROUND(km.KNSTS_RIEKI_KKK_5_TSKM / 1000) AS august_profit,
                ROUND(km.KNSTS_RIEKI_KKK_6_TSKM / 1000) AS september_profit,
                ROUND(km.KNSTS_RIEKI_KKK_7_TSKM / 1000) AS october_profit,
                ROUND(km.KNSTS_RIEKI_KKK_8_TSKM / 1000) AS november_profit,
                ROUND(km.KNSTS_RIEKI_KKK_9_TSKM / 1000) AS december_profit,
                ROUND(km.KNSTS_RIEKI_KKK_10_TSKM / 1000) AS january_profit,
                ROUND(km.KNSTS_RIEKI_KKK_11_TSKM / 1000) AS february_profit,
                ROUND(km.KNSTS_RIEKI_KKK_12_TSKM / 1000) AS march_profit
                
            FROM T_KNSTS_RIEKI_KKK_MAKER km
            
            -- メーカーマスタと結合
            LEFT JOIN M_MAKERMST mm
                ON km.MAKER_CODE = mm.MAKER_CODE
                AND mm.SHIYO_KNSH_KUBUN = '0'
            
            -- 次年度計画マスタと結合（採算管理単位名取得用）
            LEFT JOIN T_JINENDO_KKK jn
                ON km.NENDO = jn.NENDO
                AND km.SSNKN_TNCD = jn.SSNKN_TNCD
                AND km.GROUP_CODE = jn.TKY_GROUP_CODE
            
            -- 採算管理単位マスタと結合（次年度計画マスタに無い場合の代替）
            LEFT JOIN LATERAL (
                SELECT s.SSN_KANRI_TNM_KANJI
                FROM M_SAISANKANRITANIMST s
                WHERE s.SSNKN_TNCD = km.SSNKN_TNCD
                  AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') BETWEEN s.KSHB AND s.SHRYB
                  AND s.SHIYO_KNSH_KUBUN = '0'
                ORDER BY s.KSHB DESC
                LIMIT 1
            ) sm ON true
            
            -- グループマスタと結合
            LEFT JOIN LATERAL (
                SELECT g.SYSTM_UNYO_KIGYO_CODE, g.AREA_CODE, g.SUB_AREA_CODE, g.GROUP_MEI_KANJI
                FROM M_GROUPMST g
                WHERE g.GROUP_CODE = km.GROUP_CODE
                  AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') BETWEEN g.KSHB AND g.SHRYB
                  AND g.SHIYO_KNSH_KUBUN = '0'
                ORDER BY g.KSHB DESC
                LIMIT 1
            ) gm ON true
            
            -- システム運用企業マスタと結合
            LEFT JOIN LATERAL (
                SELECT sy.SYSTM_UNYO_KGYM_KANJI
                FROM M_SYSTEMUNYOKIGYOMST sy
                WHERE sy.SYSTM_UNYO_KIGYO_CODE = gm.SYSTM_UNYO_KIGYO_CODE
                  AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') BETWEEN sy.KSHB AND sy.SHRYB
                  AND sy.SHIYO_KNSH_KUBUN = '0'
                ORDER BY sy.KSHB DESC
                LIMIT 1
            ) sys ON true
            
            -- 組織エリアマスタと結合
            LEFT JOIN LATERAL (
                SELECT sa_inner.AREA_MEI_KANJI, sa_inner.SUB_AREA_MEI_KANJI
                FROM M_SOSHIKIAREAMST sa_inner
                WHERE sa_inner.AREA_CODE = gm.AREA_CODE
                  AND sa_inner.SUB_AREA_CODE = gm.SUB_AREA_CODE
                  AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') BETWEEN sa_inner.KSHB AND sa_inner.SHRYB
                  AND sa_inner.SHIYO_KNSH_KUBUN = '0'
                ORDER BY sa_inner.KSHB DESC
                LIMIT 1
            ) sa ON true
            
            WHERE 
                -- パラメータ条件
                km.NENDO = ?  -- パラメータ：会計年度
                
                -- パラメータ：本社・場所区分の条件 
                AND km.HNSH_BASHO_KUBUN %s
                
                -- パラメータ：カテゴリーコードの条件
                AND km.CTGRY_CODE %s
                
                -- エリアコードの条件（次年度計画マスタとの結合による絞り込み）
                AND EXISTS (
                    SELECT 1
                    FROM T_JINENDO_KKK jnk
                    WHERE jnk.NENDO = km.NENDO
                        AND jnk.TKY_GROUP_CODE = km.GROUP_CODE
                        AND jnk.TKY_AREA_CODE %s  -- パラメータ：エリアコード
                )
                
            -- ソート順
            ORDER BY 
                km.CTGRY_CODE ASC,           -- 1. カテゴリーコード：昇順
                km.HNSH_BASHO_KUBUN ASC,     -- 2. 本社・場所区分：昇順
                km.MAKER_CODE ASC,            -- 3. メーカーコード：昇順
                km.MAKER__KANRI_NO ASC,      -- 4. メーカ別管理No.：昇順
                km.GROUP_CODE ASC,            -- 5. グループコード：昇順
                km.ZAICHOKU_KUBUN ASC        -- 6. 在直区分：昇順
            """, hnshBashoCondition, ctgryCondition, areaCondition);
    }
}
