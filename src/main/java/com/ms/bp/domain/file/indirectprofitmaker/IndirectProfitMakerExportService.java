package com.ms.bp.domain.file.indirectprofitmaker;

import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.file.indirectprofitmaker.strategy.IndirectProfitMakerDataStrategy;
import com.ms.bp.domain.file.indirectprofitmaker.strategy.IndirectProfitMakerFileSplitStrategy;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.domain.file.base.AbstractExportService;
import com.ms.bp.domain.file.base.DataExpander;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 間接利益計画_メーカー別エクスポートサービスの実装クラス
 */
public class IndirectProfitMakerExportService extends AbstractExportService<Map<String, Object>> {
    private static final Logger logger = LoggerFactory.getLogger(IndirectProfitMakerExportService.class);

    // ==================== 定数定義 ====================

    /**
     * CSV出力列定義
     * 間接利益計画_メーカー別の全出力項目を定義
     */
    private static final List<String> CSV_COLUMNS = List.of(
"fiscal_year",           //年度
        "output_target",          //出力対象
        "ctgry_code",             //カテゴリコード
        "ctgry_mei",              // カテゴリ名
        "hnsh_basho_kubun",       // 本社・場所区分
        "hnsh_basho",             // 本社・場所
        "maker_code",             // メーカーコード
        "maker_mei",              // メーカー名
        "maker_kanri_no",         // メーカー別管理No.
        "ssnkn_tncd",             // 採算管理単位コード
        "ssn_kanri_tnm_kanji",    // 採算管理単位名
        "kigyo_code",             // 会社コード
        "kigyo_mei",              // 会社名
        "area_code",              // エリアコード
        "area_mei",               // エリア名
        "sub_area_code",          // サブエリアコード
        "sub_area_mei",           // サブエリア名
        "group_code",             // グループコード
        "group_mei",              // グループ名
        "mishu_kubun",            // 未収区分
        "mishu_mei",              // 未収名
        "zaichoku_kubun",         // 在直区分
        "zaichoku_mei",           // 在庫直送
        "april_profit",           // 4月
        "may_profit",             // 5月
        "june_profit",            // 6月
        "july_profit",            // 7月
        "august_profit",          // 8月
        "september_profit",       // 9月
        "october_profit",         // 10月
        "november_profit",        // 11月
        "december_profit",        // 12月
        "january_profit",         // 1月
        "february_profit",        // 2月
        "march_profit"            // 3月
    );

    @Override
    protected void registerSqlStrategies() {
        logger.info("間接利益計画_メーカー別エクスポート用SQL戦略を登録中...");

        // 間接利益計画_メーカー別専用のSQL戦略を登録
        sqlStrategyManager.registerStrategy(new IndirectProfitMakerDataStrategy());

        logger.info("間接利益計画_メーカー別エクスポート用SQL戦略登録完了");
        sqlStrategyManager.logStatistics();
    }

    @Override
    protected DataExpander getDataExpander() {
        // 間接利益計画_メーカー別データは展開処理が不要
        // 複雑なJOINクエリで必要なデータを直接取得するため
        return null;
    }

    @Override
    public FileSplitStrategy getSplitStrategy() {
        // 間接利益計画_メーカー別専用のファイル分割戦略を使用
        return new IndirectProfitMakerFileSplitStrategy();
    }

    @Override
    protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey) {
        // 間接利益計画_メーカー別は単一の戦略のみ使用
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .columns(CSV_COLUMNS)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .countTotal(true)
                .enableFieldMapping(true)
                .fieldHeaderMapping(createIndirectProfitMakerFieldHeaderMapping())
                .build();
    }

    /**
     * 間接利益計画_メーカー別用フィールドヘッダーマッピングを作成
     * データベース列名から日本語ヘッダー名への変換マップ
     */
    private Map<String, String> createIndirectProfitMakerFieldHeaderMapping() {
        Map<String, String> mapping = new HashMap<>();

        // 列ヘッダーを順序通りに設定
        mapping.put("fiscal_year", "年度");
        mapping.put("output_target", "出力対象");
        mapping.put("ctgry_code", "カテゴリコード");
        mapping.put("ctgry_mei", "カテゴリ名");
        mapping.put("hnsh_basho_kubun", "本社・場所区分");
        mapping.put("hnsh_basho", "本社・場所");
        mapping.put("maker_code", "メーカーコード");
        mapping.put("maker_mei", "メーカー名");
        mapping.put("maker_kanri_no", "メーカー別管理No.");
        mapping.put("ssnkn_tncd", "採算管理単位コード");
        mapping.put("ssn_kanri_tnm_kanji", "採算管理単位名");
        mapping.put("kigyo_code", "会社コード");
        mapping.put("kigyo_mei", "会社名");
        mapping.put("area_code", "エリアコード");
        mapping.put("area_mei", "エリア名");
        mapping.put("sub_area_code", "サブエリアコード");
        mapping.put("sub_area_mei", "サブエリア名");
        mapping.put("group_code", "グループコード");
        mapping.put("group_mei", "グループ名");
        mapping.put("mishu_kubun", "未収区分");
        mapping.put("mishu_mei", "未収名");
        mapping.put("zaichoku_kubun", "在直区分");
        mapping.put("zaichoku_mei", "在庫直送");
        mapping.put("april_profit", "4月");
        mapping.put("may_profit", "5月");
        mapping.put("june_profit", "6月");
        mapping.put("july_profit", "7月");
        mapping.put("august_profit", "8月");
        mapping.put("september_profit", "9月");
        mapping.put("october_profit", "10月");
        mapping.put("november_profit", "11月");
        mapping.put("december_profit", "12月");
        mapping.put("january_profit", "1月");
        mapping.put("february_profit", "2月");
        mapping.put("march_profit", "3月");

        return mapping;
    }
}