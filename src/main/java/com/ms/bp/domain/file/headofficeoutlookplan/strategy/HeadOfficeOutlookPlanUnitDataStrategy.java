package com.ms.bp.domain.file.headofficeoutlookplan.strategy;

import com.ms.bp.application.data.strategy.DataAccessStrategy;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;

/**
 * 本社_C別 SQL構築戦略インターフェース
 * 異なるエクスポートタスクに対して異なるSQLを構築するための戦略パターン
 */
public class HeadOfficeOutlookPlanUnitDataStrategy implements DataAccessStrategy {
    private static final Logger logger = LoggerFactory.getLogger(HeadOfficeOutlookPlanUnitDataStrategy.class);

    /**
     * エクスポートリクエストとユーザー情報に基づいてSQLクエリを構築
     *
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return SQLクエリとパラメータ
     */
    @Override
    public SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("本社_移管_C別_SQL構築開始: データ区分={}, エリア={},ユーザー={}", exportRequest.getDataKubun(),exportRequest.getArea(),userInfo.getShainCode());

        // 年度パラメータの設定（DateUtilを使用して次年度を取得）
        String nendoParam = DateUtil.getNextFiscalYear();

        // SQLクエリとパラメータリストを構築
        SqlWithParams queryResult = buildHeadOfficeOutlookPlanQueryWithParams(nendoParam,exportRequest.getDataKubun().getFirst(), exportRequest.getArea().getFirst());

        logger.info("本社_移管_C別_SQL構築完了: 年度={}, ユーザー={}", nendoParam, userInfo.getShainCode());
        return queryResult;
    }

    /**
     * 戦略名を取得
     *
     * @return 戦略名
     */
    @Override
    public String getStrategyName() {
        return BusinessConstants.HEAD_OFFICE_OUTLOOK_PLAN_C;
    }

    /**
     * 移管前後により異なる条件を構築
     *
     * @param nendoParam 年度
     * @param dataKubun データ区分
     * @param areaCode エリアコード
     * @return SQLクエリとパラメータ
     */
    private SqlWithParams buildHeadOfficeOutlookPlanQueryWithParams(String nendoParam,String dataKubun, String areaCode) {
        // パラメータリストを作成（順序重要）
        List<Object> paramList = new ArrayList<>();

        // 1. 年度パラメータを追加（WHERE句の最初のパラメータ）
        paramList.add(nendoParam);
        paramList.add(nendoParam);
        paramList.add(nendoParam);

        String sql="";
        String orderbyStr = "ORDER BY jinedo_mst.ctgry_code,jinedo_mst.group_code,jinedo_mst.unit_code,ssnkn_tn_mst.tntsh_code,jinedo_mst.kigyo_code,ssnkn_tn_mst.gyt_code,jinedo_mst.ssnkn_tncd";
        // 移管前（当年度組織）
       if(dataKubun.equals(BusinessConstants.DATAKUBUN_IKO_BEFORE))
       {
           sql = buildHeadofficeOutlookplanQueryWithPositionalParams(
                   String.format("LPAD( (ROW_NUMBER() OVER (%S))::text, 4, '0' )",orderbyStr),// フィールド：NO
                   "''",// フィールド：移管元部署
                   "COALESCE(jinedo_mst.iknsk_area_code,'')", // フィールド：移管先部署
                   "jinedo_mst.area_code", // フィールド/結合条件：次年度計画マスタ.エリアコード
                   "jinedo_mst.group_code",// フィールド/結合条件：次年度計画マスタ.グループコード
                   "jinedo_mst.unit_code", // フィールド：次年度計画マスタ.ユニットコード
                   "'移管前'",// フィールド：ファイル情報2
                   String.format(" jinedo_mst.area_code='%S'",areaCode),// 検索条件：エリアコード
                   orderbyStr // ソート順
           );
       }
       else
       {
           orderbyStr = orderbyStr.replace("group_code","tky_group_code").replace("unit_code","tky_unit_code");
           // 移管後（次年度組織）
           sql = buildHeadofficeOutlookplanQueryWithPositionalParams(
                   String.format("LPAD( (ROW_NUMBER() OVER (%S))::text, 4, '0' )", orderbyStr),// フィールド：NO
                   String.format("CASE WHEN jinedo_mst.iknsk_area_code = '%S' THEN jinedo_mst.area_code ELSE '' END",areaCode),// フィールド：移管元部署_B の場合　次年度計画マスタ.エリアコードを設定、上記以外は""（空）
                   "''", // フィールド：移管先部署
                   "jinedo_mst.tky_area_code", // フィールド/結合条件：次年度計画マスタ.適用エリアコード
                   "jinedo_mst.tky_group_code",// フィールド/結合条件：次年度計画マスタ.適用グループコード
                   "jinedo_mst.tky_unit_code", // フィールド：次年度計画マスタ.適用ユニットコード
                   "'移管後'",// フィールド：ファイル情報2
                   // 検索条件：適用エリアコード = {パラメータ.エリアコード} AND (A　OR　B）
                   // A_次年度計画マスタ.エリアコード={パラメータ.エリアコード} AND 次年度計画マスタ.移管先エリアコード<>""
                   // B_次年度計画マスタ.移管先エリアコード = {パラメータ.エリアコード}
                   String.format(" jinedo_mst.tky_area_code='%S'",areaCode),
                   orderbyStr // ソート順
           );
       }

        // 順序保持のためLinkedHashMapを使用してパラメータマップを構築
        Map<String, Object> paramMap = new LinkedHashMap<>();
        for (int i = 0; i < paramList.size(); i++) {
            paramMap.put("param" + i, paramList.get(i));
        }

        logger.debug("構築されたパラメータ順序: {}", paramList);
        return new SqlWithParams(sql, paramMap);
    }

    /**
     * 本社_企業別_取得用SQLクエリを構築
     *
     * @param no 発番no文字列
     * @param ikmo_area_code 移管元部署
     * @param iknsk_area_code 移管先部署
     * @param area_code エリアコード
     * @param group_code グループコード
     * @param unit_code ユニットコード
     * @param dataKubun データ区分
     * @param condition 条件
     * @param orderby ソート文字列
     * @return SQLクエリとパラメータ
     */
    private String buildHeadofficeOutlookplanQueryWithPositionalParams(String no,String ikmo_area_code,String iknsk_area_code,String area_code,
                                                                       String group_code,String unit_code,String dataKubun,String condition,String orderby){
        String sql1= """
                    -- 本社_C別
                    WITH
                    target_ssnkn AS (
                        SELECT DISTINCT jinedo_mst.ssnkn_tncd
                        FROM t_jinendo_kkk as jinedo_mst
                        WHERE jinedo_mst.nendo = ? AND %S
                    ),
                    ssnkn_jssk AS (
                       SELECT
                         ssnkn_tn_c_chkst_jssk.ssnkn_tncd,
                         ssnkn_tn_c_chkst_jssk.group_code,
                         ssnkn_tn_c_chkst_jssk.kanri_kk_nendo,
                         -- 1月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_1_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_1_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_1_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_1_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_1_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_1_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_1_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_1_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_1_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_1_tskm,

                         -- 2月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_2_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_2_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_2_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_2_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_2_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_2_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_2_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_2_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_2_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_2_tskm,

                         -- 3月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_3_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_3_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_3_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_3_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_3_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_3_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_3_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_3_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_3_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_3_tskm,

                         -- 4月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_4_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_4_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_4_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_4_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_4_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_4_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_4_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_4_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_4_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_4_tskm,

                         -- 5月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_5_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_5_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_5_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_5_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_5_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_5_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_5_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_5_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_5_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_5_tskm,

                         -- 6月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_6_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_6_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_6_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_6_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_6_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_6_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_6_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_6_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_6_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_6_tskm,

                         -- 7月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_7_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_7_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_7_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_7_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_7_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_7_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_7_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_7_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_7_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_7_tskm,

                         -- 8月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_8_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_8_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_8_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_8_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_8_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_8_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_8_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_8_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_8_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_8_tskm,

                         -- 9月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_9_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_9_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_9_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_9_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_9_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_9_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_9_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_9_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_9_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_9_tskm
                    FROM ssnkn_tn_c_chkst_jssk
                    INNER JOIN
                         target_ssnkn AS t ON ssnkn_tn_c_chkst_jssk.ssnkn_tncd = t.ssnkn_tncd
                    WHERE kanri_kk_nendo = ?
                        AND togo__kubun IN ('01', '02', '03', '04', '05', '06', '07', '08', '09', '10')
                    GROUP BY ssnkn_tn_c_chkst_jssk.kanri_kk_nendo, ssnkn_tn_c_chkst_jssk.ssnkn_tncd, ssnkn_tn_c_chkst_jssk.group_code
                    )

                    SELECT
                        -- NO
                        %S AS no,
                        --移管元部署
                        %S AS iknmt_area_code,
                        --次年度計画マスタ.移管先エリアコード
                        %S AS iknsk_area_code,
                        --次年度計画マスタ.エリアコード
                        %S AS area_code,
                        --組織エリアマスタ.エリア名漢字
                        soshiki_mst.area_mei_kanji,
                        --次年度計画マスタ.グループコード
                        %S AS group_code,
                        --次年度計画マスタ.ユニットコード
                        %S AS unit_code,
                        --担当者マスタ.担当者名漢字
                        tannto_mst.tntsh_mei_kanji,
                        --次年度計画マスタ.採算管理単位コード
                        jinedo_mst.ssnkn_tncd,
                        --採算管理単位マスタ.採算管理単位名漢字/次年度計画マスタ.採算管理単位名漢字
                        COALESCE(NULLIF(TRIM(ssnkn_tn_mst.ssn_kanri_tnm_kanji), ''),jinedo_mst.ssn_kanri_tnm_kanji) AS ssn_kanri_tnm_kanji,
                        --次年度計画マスタ.企業コード
                        jinedo_mst.kigyo_code,
                        --企業マスタ.企業名漢字
                        kigyo_mst.kgym_kanji,
                        --カテゴリマスタ.カテゴリ名漢字
                        cat_mst.ctgry_mei_kanji,
                        --カテゴリマスタ.サブカテゴリ名漢字
                        cat_sub_mst.sub_ctgry_mei_kanji,
                        --業態名マスタ.業態名
                        gyt_mei_mst.gyt_mei,
                        --次年度計画マスタ.変更後取組区分
                        jinedo_mst.hnkg_trkm_kubun,
                        --本社＿見通し・計画_採算管理単位C別.業態比率
                        hs_ssnkn_tn_c.gyt_hrts,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_1_tskm, 0) / 1000.0) AS kkk_zaiko_urg_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_1_tskm, 0) / 1000.0) AS kkk_chks_urg_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_1_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_1_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_1_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_1_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_1_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_1_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_1_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_1_tskm, 0) / 1000.0) AS kkk_chks_rieki_1_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_2_tskm, 0) / 1000.0) AS kkk_zaiko_urg_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_2_tskm, 0) / 1000.0) AS kkk_chks_urg_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_2_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_2_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_2_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_2_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_2_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_2_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_2_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_2_tskm, 0) / 1000.0) AS kkk_chks_rieki_2_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_3_tskm, 0) / 1000.0) AS kkk_zaiko_urg_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_3_tskm, 0) / 1000.0) AS kkk_chks_urg_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_3_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_3_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_3_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_3_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_3_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_3_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_3_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿３月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_3_tskm, 0) / 1000.0) AS kkk_chks_rieki_3_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_4_tskm, 0) / 1000.0) AS kkk_zaiko_urg_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_4_tskm, 0) / 1000.0) AS kkk_chks_urg_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_4_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_4_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_4_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_4_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_4_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_4_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_4_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿４月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_4_tskm, 0) / 1000.0) AS kkk_chks_rieki_4_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_5_tskm, 0) / 1000.0) AS kkk_zaiko_urg_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_5_tskm, 0) / 1000.0) AS kkk_chks_urg_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_5_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_5_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_5_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_5_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_5_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_5_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_5_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿５月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_5_tskm, 0) / 1000.0) AS kkk_chks_rieki_5_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_6_tskm, 0) / 1000.0) AS kkk_zaiko_urg_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_6_tskm, 0) / 1000.0) AS kkk_chks_urg_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_6_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_6_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_6_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_6_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_6_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_6_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_6_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿６月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_6_tskm, 0) / 1000.0) AS kkk_chks_rieki_6_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_7_tskm, 0) / 1000.0) AS kkk_zaiko_urg_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_7_tskm, 0) / 1000.0) AS kkk_chks_urg_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_7_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_7_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_7_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_7_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_7_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_7_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_7_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿７月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_7_tskm, 0) / 1000.0) AS kkk_chks_rieki_7_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_8_tskm, 0) / 1000.0) AS kkk_zaiko_urg_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_8_tskm, 0) / 1000.0) AS kkk_chks_urg_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_8_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_8_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_8_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_8_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_8_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_8_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_8_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿８月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_8_tskm, 0) / 1000.0) AS kkk_chks_rieki_8_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_9_tskm, 0) / 1000.0) AS kkk_zaiko_urg_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_9_tskm, 0) / 1000.0) AS kkk_chks_urg_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_9_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_9_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_9_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_9_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_9_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_9_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_9_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿９月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_9_tskm, 0) / 1000.0) AS kkk_chks_rieki_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_10_tskm, 0) / 1000.0) AS kkk_zaiko_urg_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_10_tskm, 0) / 1000.0) AS kkk_chks_urg_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_10_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_10_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_10_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_10_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_10_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_10_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_10_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_10_tskm, 0) / 1000.0) AS kkk_chks_rieki_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_11_tskm, 0) / 1000.0) AS kkk_zaiko_urg_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_11_tskm, 0) / 1000.0) AS kkk_chks_urg_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_11_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_11_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_11_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_11_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_11_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_11_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_11_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_11_tskm, 0) / 1000.0) AS kkk_chks_rieki_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫売上＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_12_tskm, 0) / 1000.0) AS kkk_zaiko_urg_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送売上＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_12_tskm, 0) / 1000.0) AS kkk_chks_urg_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫返品等＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_12_tskm, 0) / 1000.0) AS kkk_zaiko_hmpnt_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送返品等＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_12_tskm, 0) / 1000.0) AS kkk_chks_hmpnt_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫支払リベート＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_12_tskm, 0) / 1000.0) AS kkk_zaiko_shhr_rbt_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送支払リベート＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_12_tskm, 0) / 1000.0) AS kkk_chks_shhr_rbt_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫センターフィ＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_12_tskm, 0) / 1000.0) AS kkk_zaiko_cntr_fee_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送センターフィ＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_12_tskm, 0) / 1000.0) AS kkk_chks_cntr_fee_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿在庫利益＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_12_tskm, 0) / 1000.0) AS kkk_zaiko_rieki_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.計画＿直送利益＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_12_tskm, 0) / 1000.0) AS kkk_chks_rieki_12_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_1_tskm, 0) AS ssnkn_jssk_zaiko_urg_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_1_tskm, 0) AS ssnkn_jssk_chks_urg_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_1_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_1_tskm, 0) AS ssnkn_jssk_chks_hmpnt_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_1_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_1_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_1_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_1_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_1_tskm, 0) AS ssnkn_jssk_zaiko_rieki_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_1_tskm, 0) AS ssnkn_jssk_chks_rieki_1_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_2_tskm, 0) AS ssnkn_jssk_zaiko_urg_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_2_tskm, 0) AS ssnkn_jssk_chks_urg_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_2_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_2_tskm, 0) AS ssnkn_jssk_chks_hmpnt_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_2_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_2_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_2_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_2_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_2_tskm, 0) AS ssnkn_jssk_zaiko_rieki_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_2_tskm, 0) AS ssnkn_jssk_chks_rieki_2_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_3_tskm, 0) AS ssnkn_jssk_zaiko_urg_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_3_tskm, 0) AS ssnkn_jssk_chks_urg_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_3_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_3_tskm, 0) AS ssnkn_jssk_chks_hmpnt_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_3_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_3_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_3_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_3_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_3_tskm, 0) AS ssnkn_jssk_zaiko_rieki_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_3_tskm, 0) AS ssnkn_jssk_chks_rieki_3_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_4_tskm, 0) AS ssnkn_jssk_zaiko_urg_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_4_tskm, 0) AS ssnkn_jssk_chks_urg_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_4_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_4_tskm, 0) AS ssnkn_jssk_chks_hmpnt_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_4_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_4_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_4_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_4_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_4_tskm, 0) AS ssnkn_jssk_zaiko_rieki_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_4_tskm, 0) AS ssnkn_jssk_chks_rieki_4_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_5_tskm, 0) AS ssnkn_jssk_zaiko_urg_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_5_tskm, 0) AS ssnkn_jssk_chks_urg_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_5_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_5_tskm, 0) AS ssnkn_jssk_chks_hmpnt_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_5_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_5_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_5_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_5_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_5_tskm, 0) AS ssnkn_jssk_zaiko_rieki_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_5_tskm, 0) AS ssnkn_jssk_chks_rieki_5_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_6_tskm, 0) AS ssnkn_jssk_zaiko_urg_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_6_tskm, 0) AS ssnkn_jssk_chks_urg_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_6_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_6_tskm, 0) AS ssnkn_jssk_chks_hmpnt_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_6_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_6_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_6_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_6_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_6_tskm, 0) AS ssnkn_jssk_zaiko_rieki_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_6_tskm, 0) AS ssnkn_jssk_chks_rieki_6_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_7_tskm, 0) AS ssnkn_jssk_zaiko_urg_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_7_tskm, 0) AS ssnkn_jssk_chks_urg_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_7_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_7_tskm, 0) AS ssnkn_jssk_chks_hmpnt_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_7_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_7_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_7_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_7_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_7_tskm, 0) AS ssnkn_jssk_zaiko_rieki_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_7_tskm, 0) AS ssnkn_jssk_chks_rieki_7_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_8_tskm, 0) AS ssnkn_jssk_zaiko_urg_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_8_tskm, 0) AS ssnkn_jssk_chks_urg_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_8_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_8_tskm, 0) AS ssnkn_jssk_chks_hmpnt_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_8_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_8_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_8_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_8_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_8_tskm, 0) AS ssnkn_jssk_zaiko_rieki_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_8_tskm, 0) AS ssnkn_jssk_chks_rieki_8_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_9_tskm, 0) AS ssnkn_jssk_zaiko_urg_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_9_tskm, 0) AS ssnkn_jssk_chks_urg_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_返品(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_9_tskm, 0) AS ssnkn_jssk_zaiko_hmpnt_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_返品(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_9_tskm, 0) AS ssnkn_jssk_chks_hmpnt_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_リベート(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_9_tskm, 0) AS ssnkn_jssk_zaiko_shhr_rbt_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_リベート(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_9_tskm, 0) AS ssnkn_jssk_chks_shhr_rbt_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_ｾﾝﾀｰﾌｨ(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_9_tskm, 0) AS ssnkn_jssk_zaiko_cntr_fee_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_ｾﾝﾀｰﾌｨ(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_9_tskm, 0) AS ssnkn_jssk_chks_cntr_fee_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_9_tskm, 0) AS ssnkn_jssk_zaiko_rieki_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_9_tskm, 0) AS ssnkn_jssk_chks_rieki_9_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫売上＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_urg_10_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_urg_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送売上＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_urg_10_tskm, 0) / 1000.0) AS jssk_mtsh_chks_urg_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫返品等＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_10_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_hmpnt_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送返品等＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_10_tskm, 0) / 1000.0) AS jssk_mtsh_chks_hmpnt_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫支払リベート＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_10_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_shhr_rbt_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送支払リベート＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_10_tskm, 0) / 1000.0) AS jssk_mtsh_chks_shhr_rbt_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫センターフィ＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_10_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_cntr_fee_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送センターフィ＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_10_tskm, 0) / 1000.0) AS jssk_mtsh_chks_cntr_fee_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫利益＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_10_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_rieki_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送利益＿１０月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_rieki_10_tskm, 0) / 1000.0) AS jssk_mtsh_chks_rieki_10_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫売上＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_urg_11_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_urg_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送売上＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_urg_11_tskm, 0) / 1000.0) AS jssk_mtsh_chks_urg_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫返品等＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_11_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_hmpnt_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送返品等＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_11_tskm, 0) / 1000.0) AS jssk_mtsh_chks_hmpnt_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫支払リベート＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_11_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_shhr_rbt_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送支払リベート＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_11_tskm, 0) / 1000.0) AS jssk_mtsh_chks_shhr_rbt_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫センターフィ＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_11_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_cntr_fee_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送センターフィ＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_11_tskm, 0) / 1000.0) AS jssk_mtsh_chks_cntr_fee_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫利益＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_11_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_rieki_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送利益＿１１月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_rieki_11_tskm, 0) / 1000.0) AS jssk_mtsh_chks_rieki_11_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫売上＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_urg_12_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_urg_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送売上＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_urg_12_tskm, 0) / 1000.0) AS jssk_mtsh_chks_urg_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫返品等＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_12_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_hmpnt_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送返品等＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_12_tskm, 0) / 1000.0) AS jssk_mtsh_chks_hmpnt_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫支払リベート＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_12_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_shhr_rbt_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送支払リベート＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_12_tskm, 0) / 1000.0) AS jssk_mtsh_chks_shhr_rbt_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫センターフィ＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_12_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_cntr_fee_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送センターフィ＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_12_tskm, 0) / 1000.0) AS jssk_mtsh_chks_cntr_fee_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿在庫利益＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_12_tskm, 0) / 1000.0) AS jssk_mtsh_zaiko_rieki_12_tskm,
                        -- 本社＿見通し・計画_採算管理単位C別.実績見通し＿直送利益＿１２月目
                        ROUND(COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_rieki_12_tskm, 0) / 1000.0) AS jssk_mtsh_chks_rieki_12_tskm,
                        -- ファイル情報1
                        '見通し・計画_採算管理単位C別<本社>' AS file_info_1,
                        -- ファイル情報2
                        %S AS file_info_2
                    FROM t_jinendo_kkk AS jinedo_mst
                    -- 組織エリアマスタと関連付け
                    LEFT JOIN LATERAL (
                            SELECT area_code ,area_mei_kanji
                            FROM m_soshikiareamst
                            WHERE area_code = %S
                            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= kshb
                            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= shryb
                            AND shiyo_knsh_kubun = '0'
                            ORDER BY kshb DESC,sub_area_code
                            LIMIT 1
                        ) AS soshiki_mst ON true

                    -- 採算管理単位マスタと関連付け（必要なカラムのみ取得）
                    LEFT JOIN LATERAL (
                            SELECT
                            unit_code, tntsh_code,ssn_kanri_tnm_kanji,gyt_code,smk_code
                            FROM m_saisankanritanimst
                            WHERE ssnkn_tncd = jinedo_mst.ssnkn_tncd
                            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= kshb
                            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= shryb
                            AND shiyo_knsh_kubun = '0'
                            ORDER BY kshb DESC
                            LIMIT 1
                        ) AS ssnkn_tn_mst ON true

                    -- 担当者マスタと関連付け
                    LEFT JOIN m_tantoshamst AS tannto_mst
                         ON tannto_mst.tntsh_code = ssnkn_tn_mst.tntsh_code
                         AND tannto_mst.unit_code = ssnkn_tn_mst.unit_code
                         AND tannto_mst.shiyo_knsh_kubun = '0'

                    -- 企業情報と関連付け
                    LEFT JOIN m_kigyomst AS kigyo_mst
                         ON kigyo_mst.kigyo_code = jinedo_mst.kigyo_code
                         AND kigyo_mst.shiyo_knsh_kubun = '0'

                    -- カテゴリ情報を取得
                    LEFT JOIN LATERAL (
                        SELECT ctgry_code,ctgry_mei_kanji
                        FROM m_categorymst
                        WHERE ctgry_code = jinedo_mst.ctgry_code
                            AND shiyo_knsh_kubun = '0'
                        ORDER BY sub_ctgry_code
                        LIMIT 1
                    ) cat_mst ON true
                    
                    -- カテゴリ情報を取得
                    LEFT JOIN LATERAL (
                        SELECT ctgry_code,ctgry_mei_kanji, sub_ctgry_mei_kanji
                        FROM m_categorymst
                        WHERE ctgry_code = jinedo_mst.ctgry_code
                            AND sub_ctgry_code = jinedo_mst.sub_ctgry_code
                            AND shiyo_knsh_kubun = '0'
                        LIMIT 1
                    ) cat_sub_mst ON TRUE

                    -- 業態管理マスタと関連付け（必要なカラムのみ取得）
                    LEFT JOIN LATERAL (
                         SELECT gyt_shk_no
                         FROM m_gyt_kanri
                         WHERE gyt_code = ssnkn_tn_mst.gyt_code
                         AND smk_code = ssnkn_tn_mst.smk_code
                         AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= kshb
                         AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= shryb
                         ORDER BY kshb DESC
                         LIMIT 1
                        ) AS gyt_kanri_mst ON true

                    -- 業態名マスタと関連付け
                    LEFT JOIN m_gyt_mei AS gyt_mei_mst ON gyt_mei_mst.gyt_shk_no = gyt_kanri_mst.gyt_shk_no

                    -- 本社＿見通し・計画_採算管理単位C別と関連付け
                    LEFT JOIN LATERAL (
                         SELECT *
                         FROM t_hnsh_mtsh_kkk_ssnkn_tn_c
                         WHERE nendo= jinedo_mst.nendo
                         AND ssnkn_tncd = jinedo_mst.ssnkn_tncd
                         AND group_code = %S
                        ) hs_ssnkn_tn_c ON true

                    -- 採算管理単位C別_直接_実績と関連付け
                    LEFT JOIN ssnkn_jssk
                         ON ssnkn_jssk.kanri_kk_nendo= jinedo_mst.nendo and
                         ssnkn_jssk.ssnkn_tncd = jinedo_mst.ssnkn_tncd
                        AND (ssnkn_jssk.ssnkn_tncd <> '1118888' OR ssnkn_jssk.group_code = %S) -- 1118888の場合の処理
                """;

        String sql2= """
                    WHERE jinedo_mst.nendo = ? AND %S
                    --総売上、返品、リベート、センターフィ、直接利益の各項目いずれかが1以上の値が入っていれば出力対象
                    AND GREATEST(
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_1_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_2_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_3_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_4_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_5_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_6_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_7_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_8_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_urg_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_urg_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_hmpnt_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_hmpnt_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_shhr_rbt_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_shhr_rbt_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_cntr_fee_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_cntr_fee_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_zaiko_rieki_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.kkk_chks_rieki_12_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_1_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_2_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_3_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_4_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_5_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_6_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_7_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_8_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_9_tskm, 0),
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_9_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_urg_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_urg_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_rieki_10_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_urg_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_urg_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_rieki_11_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_urg_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_urg_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_12_tskm, 0),
                        COALESCE(hs_ssnkn_tn_c.jssk_mtsh_chks_rieki_12_tskm, 0)
                     )>= 1
                    %S
                  """;
        String fullSqlTemplate = sql1 + sql2;
        return String.format(fullSqlTemplate,condition,no,ikmo_area_code,iknsk_area_code,area_code,group_code,unit_code,dataKubun,area_code,group_code,group_code,condition,orderby);
    }
}