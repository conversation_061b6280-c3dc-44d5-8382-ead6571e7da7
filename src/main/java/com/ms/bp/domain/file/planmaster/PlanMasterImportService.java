package com.ms.bp.domain.file.planmaster;

import com.ms.bp.domain.file.model.PlanMasterImportData;
import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.repository.GroupAreaRepository;
import com.ms.bp.domain.master.repository.GroupMasterRepository;
import com.ms.bp.domain.master.repository.UnitMasterRepository;
import com.ms.bp.infrastructure.repository.impl.GroupAreaRepositoryImpl;
import com.ms.bp.infrastructure.repository.impl.GroupMasterRepositoryImpl;
import com.ms.bp.infrastructure.repository.impl.UnitMasterRepositoryImpl;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DTODataValidator;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.shared.common.io.cache.ImportSessionCache;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.domain.master.service.MasterDataEnrichmentService;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.ExportFileNameUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.shared.util.RequestContext;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

/**
 * 次年度計画マスタインポートサービスの実装クラス
 * ファイル処理領域における次年度計画マスタデータのインポート処理を担当
 * 
 * 主な機能：
 * 1. CSVファイルの解析とデータ検証
 * 2. 権限チェック（エリアコード、移管先エリアコード）
 * 3. データベース存在性チェック（移管先マスタ）
 * 4. T_JINENDO_KKK表への自動UPSERT処理
 */
public class PlanMasterImportService extends AbstractImportService<PlanMasterImportData> {

    private static final Logger logger = LoggerFactory.getLogger(PlanMasterImportService.class);
    private static final String EMPTY_CONTAINER = "1118888";

    /**
     * コンストラクタ
     */
    public PlanMasterImportService() {
        logger.debug("PlanMasterImportServiceが初期化されました");
    }

    @Override
    protected String getDataType() {
        return BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE; // 次年度計画マスタのデータタイプコード
    }

    @Override
    protected DataValidator getDataValidator() {
        // 領域モデルベースのバリデーターを使用（アノテーション検証 + カスタム検証）
        return new DTODataValidator<>(PlanMasterImportData.class, this::validateCustomLogic);
    }

    @Override
    protected Class<PlanMasterImportData> getDTOClass() {
        return PlanMasterImportData.class;
    }

    @Override
    protected ImportOptions buildImportOptions() {
        // CSVヘッダーフィールドマッピングを構築
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("エリアCD", "areaCode");
        headerMapping.put("エリア名", "areaName"); // 参考用、DBには保存しない
        headerMapping.put("カテCD", "categoryCode");
        headerMapping.put("カテゴリ", "categoryName"); // 参考用
        headerMapping.put("グループ", "groupCode");
        headerMapping.put("ユニット", "unitCode");
        headerMapping.put("担当者", "tantoshaName");
        headerMapping.put("企業CD", "kigyoCode");
        headerMapping.put("企業名", "kigyoName"); // 参考用
        headerMapping.put("業態集計", "gyotaiShukei");
        headerMapping.put("業態名", "gyotaiName");
        headerMapping.put("サブカテゴリ", "subCategoryCode");
        headerMapping.put("サブカテゴリ名", "subCategoryName"); // 参考用
        headerMapping.put("採算管理単位CD", "saisaknKanriTaniCode");
        headerMapping.put("採算管理単位名", "saisaknKanriTaniName");
        headerMapping.put("変更前取組区分", "henkomaeTorkumiKubun"); // 参考用
        headerMapping.put("変更後取組区分", "henkogoTorikumiKubun");
        headerMapping.put("移管先エリアCD", "ikansakiAreaCode");
        headerMapping.put("移管先エリア名", "ikansakiAreaName"); // 参考用
        headerMapping.put("移管先グループCD", "ikansakiGroupCode");
        headerMapping.put("移管先ユニットCD", "ikansakiUnitCode");
        headerMapping.put("当年度実績累計(参考)", "currentYearActual"); // 参考用
        headerMapping.put("当年度計画累計(参考)", "currentYearPlan"); // 参考用

        // 固定値フィールドを構築（RequestContextからユーザー情報を取得）
        Map<String, Object> additionalFields = buildAdditionalFields();

        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .batchSize(BusinessConstants.CSV_UPLOAD_BATCH_SIZE)
                .targetTable("T_JINENDO_KKK")
                // 複合主キー：年度 + 採算管理単位コード + グループコード
                .keyColumns("NENDO", "SSNKN_TNCD", "GROUP_CODE")
                .upsertMode(true)
                .skipValidation(false)
                .continueOnError(false)
                .errorFileName(ExportFileNameUtil.getFileNameByFileType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE))
                .enableFieldMapping(true)  // フィールドマッピングを有効化
                .headerFieldMapping(headerMapping)
                .additionalFields(additionalFields)
                .build();
    }

    /**
     * 固定値フィールドを構築（RequestContextからユーザー情報を取得）
     */
    private Map<String, Object> buildAdditionalFields() {
        Map<String, Object> additionalFields = new HashMap<>();

        // RequestContextからユーザー情報を取得
        UserInfo userInfo = RequestContext.getUserInfo();

        additionalFields.put("NENDO", DateUtil.getNextFiscalYear());
        if (userInfo != null) {
            // ユーザー情報が取得できた場合、実際の値を設定
            String companyCode = userInfo.getSystemOperationCompanyCode();
            String shainCode = userInfo.getShainCode();

            additionalFields.put("SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("TRK_SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("TRK_SHAIN_CODE", shainCode);
            additionalFields.put("KSHN_SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("KSHN_SHAIN_CODE", shainCode);

            logger.debug("ユーザー情報から固定値フィールドを設定: 企業コード={}, 社員コード={}",
                        companyCode, shainCode);
        }

        return additionalFields;
    }

    /**
     * カスタムビジネスロジック検証
     * アノテーション検証では対応できない複雑な検証ロジックを実装
     *
     * 検証内容：
     * 1. 権限チェック（エリアコード、移管先エリアコード）
     * 2. データベース存在性チェック（移管先エリア、グループ、ユニット）
     * 3. 業務ルールに基づく検証
     *
     * @param data 検証対象のデータ
     * @param options インポートオプション
     * @return 検証エラーのリスト
     */
    protected List<ValidationError> validateCustomLogic(Map<String, Object> data, ImportOptions options) {
        List<ValidationError> errors = new ArrayList<>();

        // RequestContextからユーザー情報を取得して権限チェック
        UserInfo userInfo = RequestContext.getUserInfo();
        if (userInfo != null) {
            validatePermissions(data, userInfo, errors);
        }

        // データベース存在性チェック
        validateDatabaseReferences(data, errors);

        return errors;
    }

    /**
     * 権限チェック処理
     */
    private void validatePermissions(Map<String, Object> data, UserInfo userInfo, List<ValidationError> errors) {
        logger.debug("権限チェック開始: ユーザー={}", userInfo.getShainCode());

        // エリアコードと移管先エリアコードの権限チェック
        String areaCode = (String) data.get("areaCode");
        String ikansakiAreaCode = (String) data.get("ikansakiAreaCode");

        if (hasValue(areaCode) && hasValue((ikansakiAreaCode))) {
            if (!validateAreaPermission(areaCode, userInfo) && !validateAreaPermission(ikansakiAreaCode, userInfo)) {
                errors.add(new ValidationError("エリアCD", areaCode, formatMessage(GlobalMessageConstants.ERR_027, "エリアCD")));
                errors.add(new ValidationError("移管先エリアCD", ikansakiAreaCode, formatMessage(GlobalMessageConstants.ERR_027, "移管先エリアCD")));
            }
        } else if (hasValue(areaCode)) {
            if (!validateAreaPermission(areaCode, userInfo)) {
                errors.add(new ValidationError("エリアCD", areaCode, formatMessage(GlobalMessageConstants.ERR_027, "エリアCD")));
            }
        } else if (hasValue(ikansakiAreaCode)) {
            if (!validateAreaPermission(ikansakiAreaCode, userInfo)) {
                errors.add(new ValidationError("移管先エリアCD", ikansakiAreaCode, formatMessage(GlobalMessageConstants.ERR_027, "移管先エリアCD")));
            }
        }

        logger.debug("権限チェック完了: エラー数={}", errors.size());
    }

    /**
     * 値があるかどうかチェック
     */
    private boolean hasValue(String value) {
        return value != null && !value.trim().isEmpty();
    }

    /**
     * エリア権限検証
     * ユーザーのエリア権限に基づいてアクセス可能なエリアコードかどうかを検証する
     * 検証ロジック：
     * 1. userInfo.areaInfosが設定されている場合：areaInfos内のareaCodeと照合
     * 2. userInfo.areaInfosが空の場合：userInfo.areaCodeと照合
     * 3. いずれにも該当しない場合は権限エラー
     */
    private boolean validateAreaPermission(String areaCode, UserInfo userInfo) {
        boolean hasPermission = true;
        List<AreaInfo> areaInfos = userInfo.getAreaInfos();

        if (areaInfos != null && !areaInfos.isEmpty()) {
            // areaInfosが設定されている場合：コレクション内のareaCodeと照合
            hasPermission = areaInfos.stream()
                    .anyMatch(areaInfo -> areaCode.equals(areaInfo.getAreaCode()));
        }

        return hasPermission;
    }

    /**
     * 空容器データチェックとデータベース存在性チェック
     * LambdaResourceManagerを使用してリソース管理を統一化
     * Repository実例は使用時に作成し、接続リークを防止
     */
    private void validateDatabaseReferences(Map<String, Object> data, List<ValidationError> errors) {
        try {
            String saisaknKanriTaniCode = (String) data.get("saisaknKanriTaniCode");
            boolean saisaCodeFlag = saisaknKanriTaniCode != null && !saisaknKanriTaniCode.trim().isEmpty()
                     && saisaknKanriTaniCode.equals(EMPTY_CONTAINER);// 採算管理単位CDが'1118888'(空容器)

            // LambdaResourceManagerを使用してデータベース操作を実行
            LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
                // Repository インスタンスを作成（キャッシュしない）
                GroupAreaRepository groupAreaRepository = new GroupAreaRepositoryImpl(jdbcTemplate);
                GroupMasterRepository groupMasterRepository = new GroupMasterRepositoryImpl(jdbcTemplate);
                UnitMasterRepository unitMasterRepository = new UnitMasterRepositoryImpl(jdbcTemplate);

                // 移管先エリアコードの存在チェック
                String ikansakiAreaCode = (String) data.get("ikansakiAreaCode");
                if (ikansakiAreaCode != null && !ikansakiAreaCode.trim().isEmpty()) {
                    // 採算管理単位CDが'1118888'(空容器)だった場合は登録不可
                    if(saisaCodeFlag){
                        errors.add(new ValidationError("ikansakiAreaCode", ikansakiAreaCode,
                                formatMessage(GlobalMessageConstants.ERR_032, "移管先エリアCD")));
                    }
                    else if (!checkMasterExists("組織エリアマスタ", ikansakiAreaCode,
                            () -> groupAreaRepository.existsByAreaCode(ikansakiAreaCode))) {
                        errors.add(new ValidationError("ikansakiAreaCode", ikansakiAreaCode,
                                formatMessage(GlobalMessageConstants.ERR_022,"移管先エリアCD","CSV.移管先エリアCD")));
                    }
                }

                // 移管先グループコードの存在チェック
                String ikansakiGroupCode = (String) data.get("ikansakiGroupCode");
                if (ikansakiGroupCode != null && !ikansakiGroupCode.trim().isEmpty()) {
                    // 採算管理単位CDが'1118888'(空容器)だった場合は登録不可
                    if(saisaCodeFlag){
                        errors.add(new ValidationError("ikansakiGroupCode", ikansakiGroupCode,
                                formatMessage(GlobalMessageConstants.ERR_032, "移管先グループCD")));
                    }
                    else if (!checkMasterExists("グループマスタ", ikansakiGroupCode,
                            () -> groupMasterRepository.existsByGroupCode(ikansakiGroupCode))) {
                        errors.add(new ValidationError("ikansakiGroupCode", ikansakiGroupCode,
                                formatMessage(GlobalMessageConstants.ERR_022,"移管先グループCD","CSV.移管先グループCD")));
                    }
                }

                // 移管先ユニットコードの存在チェック
                String ikansakiUnitCode = (String) data.get("ikansakiUnitCode");
                if (ikansakiUnitCode != null && !ikansakiUnitCode.trim().isEmpty()) {
                    // 採算管理単位CDが'1118888'(空容器)だった場合は登録不可
                    if(saisaCodeFlag){
                        errors.add(new ValidationError("ikansakiUnitCode", ikansakiUnitCode,
                                formatMessage(GlobalMessageConstants.ERR_032, "移管先ユニットCD")));
                    }
                    else if (!checkMasterExists("ユニットマスタ", ikansakiUnitCode,
                            () -> unitMasterRepository.existsByUnitCode(ikansakiUnitCode))) {
                        errors.add(new ValidationError("ikansakiUnitCode", ikansakiUnitCode,
                                formatMessage(GlobalMessageConstants.ERR_022,"移管先ユニットCD","CSV.移管先ユニットCD")));
                    }
                }

                // 採算管理単位コードの特別処理
                // 要求仕様：採算管理単位マスタにない採算管理単位コードも入力可能
                // そのため、存在チェックは行わない

                return null; // 戻り値は不要
            });

        } catch (Exception e) {
            logger.error("データベース存在性チェック中にエラーが発生しました", e);
            errors.add(new ValidationError("database", "connection",
                    "データベース接続エラー: " + e.getMessage()));
        }
    }

    /**
     * マスタデータの存在チェック（汎用メソッド）
     *
     * @param masterName マスタ名（ログ出力用）
     * @param code 検証対象のコード
     * @param existsCheck 存在チェック処理
     * @return 存在する場合true、存在しない場合またはエラーの場合false
     */
    private boolean checkMasterExists(String masterName, String code, Supplier<Boolean> existsCheck) {
        try {
            return existsCheck.get();
        } catch (Exception e) {
            logger.error("{}存在チェック中にエラーが発生しました: code={}", masterName, code, e);
            // エラーが発生した場合は存在しないものとして扱う
            return true;
        }
    }



    @Override
    protected void enrichMasterData(List<PlanMasterImportData> dtos,
                                    JdbcTemplate template,
                                    ImportSessionCache cache) {
        if (dtos == null || dtos.isEmpty()) return;

        logger.debug("次年度計画マスタのマスタデータ補完開始: 対象DTO数={}", dtos.size());

        try {
            var enrichmentService = new MasterDataEnrichmentService();

            // 必要なコードを収集してマスタデータを一括取得
            var unitCodes = dtos.stream()
                    .map(PlanMasterImportData::getUnitCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (unitCodes.isEmpty()) return;

            var unitNames = enrichmentService.getUnitNames(
                    List.copyOf(unitCodes), template, cache);

            // 各DTOにマスタデータを適用
            dtos.forEach(dto -> {
                var masterDataMap = new HashMap<String, Object>();

                // ユニット名称を設定
                Optional.ofNullable(dto.getUnitCode())
                        .filter(unitNames::containsKey)
                        .ifPresent(unitCode ->
                                masterDataMap.put("UNIT_NAME_" + unitCode, unitNames.get(unitCode)));

                dto.applyMasterData(masterDataMap);
            });

        } catch (Exception e) {
            logger.warn("次年度計画マスタのマスタデータ補完中にエラーが発生しました: {}", e.getMessage(), e);
        }
    }
}
