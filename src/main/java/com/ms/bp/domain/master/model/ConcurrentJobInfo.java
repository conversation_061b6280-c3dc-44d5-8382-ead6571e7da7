package com.ms.bp.domain.master.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 兼務情報ドメインモデル
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConcurrentJobInfo {
    
    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;
    
    /**
     * 社員コード
     */
    private String shainCode;
    
    /**
     * ユニットコード（兼務先）
     */
    private String unitCode;
    
    /**
     * エリアコード（ユニットコードに対応）
     */
    private String areaCode;
    
    /**
     * 役職区分
     */
    private String positionDivision;

    /**
     * グループコード
     */
    private String groupCode;
}