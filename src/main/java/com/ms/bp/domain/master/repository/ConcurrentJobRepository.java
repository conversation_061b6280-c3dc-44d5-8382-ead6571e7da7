package com.ms.bp.domain.master.repository;


import com.ms.bp.domain.master.model.ConcurrentJobInfo;

import java.util.List;

/**
 * 兼務マスタリポジトリ領域インターフェース
 */
public interface ConcurrentJobRepository {

    /**
     * 指定社員の有効な兼務情報をエリアコード・グループコード付きで取得
     * 兼務マスタ、ユニットマスタ、グループマスタを連携して一括取得
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 有効な兼務情報リスト（エリアコード・グループコード付き、該当データがない場合は空のリスト）
     */
    List<ConcurrentJobInfo> findConcurrentJobsWithAreaInfo(String shainCode, String systemOperationCompanyCode);

}