package com.ms.bp.domain.master.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * ユニット・グループ統合情報値オブジェクト
 * ユニットマスタとグループマスタの結合情報を表現する値オブジェクト
 * パフォーマンス最適化のため単一クエリで取得する統合データを保持
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UnitGroupInfo {
    
    /**
     * ユニットコード（5桁）
     * M_UNITMST.UNIT_CODE
     */
    private String unitCode;
    
    /**
     * ユニット名
     * M_UNITMST.UNIT_MEI
     */
    private String unitName;
    
    /**
     * グループコード
     * M_UNITMST.GROUP_CODE / M_GROUPMST.GROUP_CODE
     */
    private String groupCode;
    
    /**
     * グループ名
     * M_GROUPMST.GROUP_MEI
     */
    private String groupName;
}
