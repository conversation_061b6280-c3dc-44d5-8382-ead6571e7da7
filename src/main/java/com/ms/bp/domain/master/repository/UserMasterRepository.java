package com.ms.bp.domain.master.repository;

import com.ms.bp.domain.master.model.UserBasicInfo;

import java.util.List;
import java.util.Optional;

/**
 * ユーザーマスタリポジトリ
 * 社員マスタ領域のデータ永続化を抽象化
 * M_SHAINMST（社員マスタ）に対応
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public interface UserMasterRepository {

    /**
     * ユーザーコードとシステム運用企業コードでユーザー基本情報を取得
     * 社員マスタ、ユニットマスタ、グループマスタ、組織エリアマスタを連携して一括取得
     * 使用禁止区分が'1'でない有効なレコードのみを対象とする
     *
     * @param systemOperationCompanyCode システム運用企業コード（6桁）
     * @param shainCode 社員コード（6桁）
     * @param positionCodeList 役職区分コード列表（権限処理時のフィルタリング用、nullの場合はフィルタリングしない）
     * @return ユーザー基本情報（ユニットコード、役職区分コード、エリアコード、エリア名称を含む）
     */
    Optional<UserBasicInfo> findUserBasicInfo(String systemOperationCompanyCode, String shainCode, List<String> positionCodeList);
}
