package com.ms.bp.domain.master.service;

import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.common.io.cache.ImportSessionCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * マスタデータ補完ドメインサービス
 * インポート処理中のマスタデータ取得と補完を支援するドメイン層サービス
 * 
 * DDD分層原則に従い、domain層に配置
 * 主な機能：
 * - 批量查询优化
 * - キャッシュ機能
 * - エラーハンドリング
 */
public class MasterDataEnrichmentService {
    private static final Logger logger = LoggerFactory.getLogger(MasterDataEnrichmentService.class);

    /**
     * ユニットコードからユニット名称を一括取得
     * 
     * @param unitCodes ユニットコードのリスト
     * @param template JDBCテンプレート
     * @param cache セッションキャッシュ
     * @return ユニットコードとユニット名称のマップ
     */
    public Map<String, String> getUnitNames(List<String> unitCodes,
                                            JdbcTemplate template,
                                            ImportSessionCache cache) {
        if (unitCodes == null || unitCodes.isEmpty()) {
            return Map.of();
        }

        // 重複を除去
        var uniqueUnitCodes = unitCodes.stream()
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (uniqueUnitCodes.isEmpty()) {
            return Map.of();
        }

        // キャッシュから取得とキャッシュ未登録コードの分離
        var result = new HashMap<String, String>();
        var uncachedCodes = uniqueUnitCodes.stream()
                .filter(unitCode -> {
                    var cacheKey = ImportSessionCache.generateKey("UNIT", "UNIT_CODE", unitCode);
                    var cachedName = cache.get(cacheKey);
                    if (cachedName != null) {
                        result.put(unitCode, (String) cachedName);
                        return false; // キャッシュにあるので除外
                    }
                    return true; // キャッシュにないので含める
                })
                .toList();

        // キャッシュにないデータをデータベースから取得
        if (!uncachedCodes.isEmpty()) {
            try {
                var dbResult = queryUnitNamesFromDatabase(uncachedCodes, template);

                // 結果をキャッシュに保存
                dbResult.forEach((unitCode, unitName) -> {
                    var cacheKey = ImportSessionCache.generateKey("UNIT", "UNIT_CODE", unitCode);
                    cache.put(cacheKey, unitName);
                });

                result.putAll(dbResult);
                logger.debug("ユニット名称取得完了: DB取得={}, キャッシュ取得={}",
                        dbResult.size(), uniqueUnitCodes.size() - uncachedCodes.size());
            } catch (Exception e) {
                logger.warn("ユニット名称取得中にエラーが発生しました: {}", e.getMessage(), e);
            }
        }

        return result;
    }


    /**
     * データベースからユニット名称を取得
     */
    private Map<String, String> queryUnitNamesFromDatabase(List<String> unitCodes, 
                                                          JdbcTemplate template) throws SQLException {
        String placeholders = String.join(",", Collections.nCopies(unitCodes.size(), "?"));
        String sql = String.format("""
            SELECT 
                UNIT_CODE,
                REGEXP_REPLACE(unit_mei_tnshk_kanji, '\\s', '', 'g') as unit_name
            FROM M_UNITMST
            WHERE UNIT_CODE IN (%s)
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
            AND SHIYO_KNSH_KUBUN = '0'
            """, placeholders);

        List<Map<String, String>> results = template.query(sql, unitCodes.toArray(), rs -> {
            Map<String, String> result = new HashMap<>();
            result.put(rs.getString("UNIT_CODE"), rs.getString("unit_name"));
            return result;
        });
        
        Map<String, String> finalResult = new HashMap<>();
        for (Map<String, String> map : results) {
            finalResult.putAll(map);
        }
        return finalResult;
    }
}
