package com.ms.bp.domain.master.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * システム管理者情報ドメインモデル
 * システム管理者の権限情報を表現するドメインモデル
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemAdminInfo {
    
    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;
    
    /**
     * 社員コード
     */
    private String shainCode;

    /**
     * 使用禁止区分
     */
    private String prohibitionFlag;
}