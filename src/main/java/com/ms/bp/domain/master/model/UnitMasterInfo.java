package com.ms.bp.domain.master.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * ユニットマスタ情報値オブジェクト
 * ユニットマスタから取得するユニット情報を表現する値オブジェクト
 * M_UNITMST（ユニットマスタ）の関連データを保持
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UnitMasterInfo {
    
    /**
     * ユニットコード（5桁）
     * M_UNITMST.UNIT_CODE
     */
    private String unitCode;
    
    /**
     * ユニット名
     * M_UNITMST.UNIT_MEI
     */
    private String unitName;
    
    /**
     * グループコード
     * M_UNITMST.GROUP_CODE
     */
    private String groupCode;
}
