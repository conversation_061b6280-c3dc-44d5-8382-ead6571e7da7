package com.ms.bp.domain.master.repository;

import com.ms.bp.domain.master.model.UnitMasterInfo;
import com.ms.bp.domain.master.model.UnitGroupInfo;
import java.util.Optional;

/**
 * ユニットマスタリポジトリ
 * ユニットマスタ領域のデータ永続化を抽象化
 * M_UNITMST（ユニットマスタ）に対応
 */
public interface UnitMasterRepository {

    /**
     * ユニットコードでユニットマスタの存在チェック
     * 指定されたユニットコードがユニットマスタに存在するかを確認する
     *
     * @param unitCode ユニットコード（5桁）
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsByUnitCode(String unitCode);


    /**
     * ユニットコードでユニット・グループ統合情報を取得
     * 単一クエリでユニットマスタとグループマスタを結合して取得
     *
     * ビジネスルール：
     * - ユニットマスタ：有効期間内（終了日 >= システム日付）
     * - グループマスタ：有効期間内（終了日 >= システム日付）、使用禁止区分='0'
     * - 関連するユニットマスタ条件も満たす必要がある
     * - 複数件取得できた場合は1件目を使用
     *
     * @param unitCode ユニットコード（5桁）
     * @return ユニット・グループ統合情報（該当データがない場合は空のOptional）
     */
    Optional<UnitGroupInfo> findUnitGroupInfoByUnitCode(String unitCode);
}
