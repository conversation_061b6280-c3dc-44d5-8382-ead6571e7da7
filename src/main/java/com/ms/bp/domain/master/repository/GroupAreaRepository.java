package com.ms.bp.domain.master.repository;

import com.ms.bp.domain.master.model.AreaInfo;

import java.sql.SQLException;
import java.util.List;

/**
 * 組織エリアマスタリポジトリ
 * 組織エリアマスタ領域のデータ永続化を抽象化
 * M_SOSHIKIAREAMST（組織エリアマスタ）に対応
 */
public interface GroupAreaRepository {

    /**
     * エリアコードで組織エリアマスタの存在チェック
     * 指定されたエリアコードが組織エリアマスタに存在するかを確認する
     *
     * @param areaCode エリアコード（4桁）
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsByAreaCode(String areaCode);

    /**
     * エリアコードでエリア情報リストを取得
     * M_SOSHIKIAREAMSTを関連付けてエリアコードとエリア名称を取得
     *
     * @param areaCodes エリアコードの組み合わせの文字列
     * @return エリア情報リスト（エリア表示順でソート済み）
     */
    List<AreaInfo> findAreaInfosByAreaCodes(String areaCodes);
}
