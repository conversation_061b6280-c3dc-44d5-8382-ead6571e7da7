package com.ms.bp.domain.master.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * グループマスタ情報値オブジェクト
 * グループマスタから取得するグループ情報を表現する値オブジェクト
 * M_GROUPMST（グループマスタ）の関連データを保持
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroupMasterInfo {
    
    /**
     * グループコード
     * M_GROUPMST.GROUP_CODE
     */
    private String groupCode;
    
    /**
     * グループ名
     * M_GROUPMST.GROUP_MEI
     */
    private String groupName;
    
    /**
     * 引継元ユニットコード
     * M_GROUPMST.HKMT_UNIT_CODE
     */
    private String inheritanceUnitCode;
}
