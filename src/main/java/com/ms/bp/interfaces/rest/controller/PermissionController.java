package com.ms.bp.interfaces.rest.controller;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.application.PermissionApplicationService;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponseV2;
import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.shared.util.ResponseUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 権限RESTコントローラー
 * 権限関連のAPIエンドポイントを提供する
 */
public class PermissionController {
    private static final Logger logger = LoggerFactory.getLogger(PermissionController.class);
    
    private final PermissionApplicationService permissionService;
    private final ObjectMapper objectMapper;

    public PermissionController() {
        this.permissionService = new PermissionApplicationService();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * ユーザー権限一覧取得API（ページ初期化用）
     * GET /api/permissions/user
     */
    public APIGatewayProxyResponseEvent getUserPermissions(APIGatewayProxyRequestEvent request, UserInfo userInfo) {
        try {
            logger.info("ユーザー権限一覧取得要求を受信しました: ユーザー={}", userInfo.getShainCode());

            // リクエストボディからファイル種別パラメータを取得
            String operationType = null;
            String requestBody = request.getBody();
            if (ObjectUtils.isNotEmpty(requestBody)) {
                // JSONからパラメータを解析
                var requestData = objectMapper.readValue(requestBody, Map.class);
                operationType = (String) requestData.get("operationType");
            }

            // Application Service に委譲（DTO変換も含む）
            UserPermissionsResponseV2 response = permissionService.getUserPermissions(userInfo, null,null,null,null,null, operationType);

            // 成功レスポンスを返却
            CommonResult<UserPermissionsResponseV2> result = CommonResult.success(response);
            return ResponseUtil.toApiGatewayResponse(result);

        } catch (Exception e) {
            logger.error("ユーザー権限一覧取得処理中にエラーが発生しました", e);
            CommonResult<String> errorResponse = CommonResult.error(50001, e.getMessage());
            return ResponseUtil.toApiGatewayResponse(errorResponse);
        }
    }

}
