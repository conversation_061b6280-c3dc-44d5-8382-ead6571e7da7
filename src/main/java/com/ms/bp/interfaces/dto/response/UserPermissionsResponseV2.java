package com.ms.bp.interfaces.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * ユーザー権限一覧応答DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserPermissionsResponseV2 {

    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;

    /**
     * 社員コード
     */
    private String shainCode;

    /**
     * 画面表示可否 (0:表示可 1:表示不可)
     */
    private String screenDisplayFlag;

    /**
     * システム管理者フラグ (0:一般ユーザー 1:システム管理者)
     */
    private String systemAdminFlag;

    /**
     * ロールリスト（職務組み合わせリスト）
     */
    private List<RoleInfo> roleList;

    // ==================== 内部クラス ====================

    /**
     * ロール情報DTO（職務組み合わせ情報）
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RoleInfo {

        /**
         * エリアコード
         */
        private String areaCode;

        /**
         * ユニットコード
         */
        private String unitCode;

        /**
         * ユニット名
         */
        private String unitName;

        /**
         * グループコード
         */
        private String groupCode;

        /**
         * グループ名
         */
        private String groupName;

        /**
         * 役職区分判定要否 (0:否 1:要)
         */
        private String positionSpecialCheck;

        /**
         * 役職区分
         */
        private String positionCode;

        /**
         * 役職区分名
         */
        private String positionName;

        /**
         * 権限リスト
         */
        private List<PermissionInfo> permissionList;

        /**
         * エリアリスト
         */
        private List<AreaInfo> areaList;
    }

    /**
     * 権限情報DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PermissionInfo {

        /**
         * 権限コード
         */
        private String permissionCode;

        /**
         * ファイル種別
         */
        private String fileType;

        /**
         * 操作区分
         */
        private String operationDivision;

        /**
         * エリアパターン
         */
        private String areaPattern;
    }

    /**
     * エリア情報DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AreaInfo {

        /**
         * エリアコード
         */
        private String areaCode;

        /**
         * エリア名
         */
        private String areaName;
    }
}
